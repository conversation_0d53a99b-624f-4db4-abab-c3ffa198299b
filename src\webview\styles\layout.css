/* 布局系统 */

/* 基础布局 */
body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-foreground);
  background-color: var(--color-surface);
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* 主容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-surface);
  position: relative;
}

/* 聊天容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* 消息区域 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  background-color: var(--color-background);
  scroll-behavior: smooth;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 消息内容包装器 */
.messages-wrapper {
  width: 100%;
  max-width: var(--message-container-max-width);
  margin: 0 auto;
}

/* 自定义滚动条 */
.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background-color: var(--vscode-scrollbarSlider-background);
}

.messages-container::-webkit-scrollbar-thumb {
  background-color: var(--vscode-scrollbarSlider-background);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--vscode-scrollbarSlider-hoverBackground);
}

/* 输入区域 */
.input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  background-color: var(--color-surface);
  position: relative;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
  width: 100%;
  max-width: var(--message-container-max-width);
}

.input-field {
  flex: 1;
  min-height: var(--input-min-height);
  max-height: var(--input-max-height);
  resize: none;
  overflow-y: auto;
  padding-right: 40px; /* 为发送按钮留空间 */
}

.send-button {
  position: absolute;
  right: var(--spacing-md);
  bottom: var(--spacing-md);
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--color-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  opacity: 0.6;
  transition: opacity var(--transition-fast);
  border-radius: var(--radius-sm);
}

.send-button:hover {
  opacity: 1;
  background-color: var(--color-surface-hover);
}

.send-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* 设置页面布局 */
.settings-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-background);
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-surface);
}

.settings-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.settings-section {
  margin-bottom: var(--spacing-3xl);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-lg);
  color: var(--color-foreground);
}

.settings-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) 0;
  border-bottom: 1px solid var(--color-border);
}

.settings-item:last-child {
  border-bottom: none;
}

.settings-item-info {
  flex: 1;
}

.settings-item-label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
}

.settings-item-description {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
  line-height: var(--line-height-relaxed);
}

.settings-item-control {
  margin-left: var(--spacing-lg);
}

/* 模态框布局 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.95);
  transition: transform var(--transition-normal);
}

.modal-overlay.show .modal {
  transform: scale(1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  :root {
    --message-container-max-width: 900px;
  }
}

@media (max-width: 900px) {
  :root {
    --message-container-max-width: 100%;
    --message-user-max-width: 85%;
    --message-ai-max-width: 95%;
  }
}

@media (max-width: 768px) {
  :root {
    --message-user-max-width: 90%;
    --message-ai-max-width: 100%;
  }

  .messages-container {
    padding: var(--spacing-sm);
  }

  .input-container {
    padding: var(--spacing-sm);
  }

  .settings-content {
    padding: var(--spacing-md);
  }

  .settings-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .settings-item-control {
    margin-left: 0;
    width: 100%;
  }
}

@media (max-width: 480px) {
  :root {
    --message-user-max-width: 95%;
  }

  .messages-container {
    padding: var(--spacing-xs);
  }

  .input-container {
    padding: var(--spacing-xs);
  }
}

/* 打印样式 */
@media print {
  .input-container,
  .message-actions,
  .toast-container {
    display: none !important;
  }
  
  .messages-container {
    overflow: visible !important;
    height: auto !important;
  }
  
  .message-content {
    break-inside: avoid;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .message-content {
    border-width: 2px;
  }
  
  .btn {
    border-width: 2px;
  }
  
  .input {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 焦点管理 */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* 响应式网格系统 */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--grid-margin);
  padding-right: var(--grid-margin);
}

.container-xs { max-width: var(--container-xs); }
.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

.grid {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns), 1fr);
  gap: var(--grid-gap);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

/* 网格列跨度 */
.col-1 { grid-column: span 1; }
.col-2 { grid-column: span 2; }
.col-3 { grid-column: span 3; }
.col-4 { grid-column: span 4; }
.col-5 { grid-column: span 5; }
.col-6 { grid-column: span 6; }
.col-7 { grid-column: span 7; }
.col-8 { grid-column: span 8; }
.col-9 { grid-column: span 9; }
.col-10 { grid-column: span 10; }
.col-11 { grid-column: span 11; }
.col-12 { grid-column: span 12; }

/* 响应式断点 */
@media (min-width: 480px) {
  .xs\:col-1 { grid-column: span 1; }
  .xs\:col-2 { grid-column: span 2; }
  .xs\:col-3 { grid-column: span 3; }
  .xs\:col-4 { grid-column: span 4; }
  .xs\:col-6 { grid-column: span 6; }
  .xs\:col-12 { grid-column: span 12; }
  .xs\:flex { display: flex; }
  .xs\:hidden { display: none; }
}

@media (min-width: 640px) {
  .sm\:col-1 { grid-column: span 1; }
  .sm\:col-2 { grid-column: span 2; }
  .sm\:col-3 { grid-column: span 3; }
  .sm\:col-4 { grid-column: span 4; }
  .sm\:col-6 { grid-column: span 6; }
  .sm\:col-12 { grid-column: span 12; }
  .sm\:flex { display: flex; }
  .sm\:hidden { display: none; }
}

@media (min-width: 768px) {
  .md\:col-1 { grid-column: span 1; }
  .md\:col-2 { grid-column: span 2; }
  .md\:col-3 { grid-column: span 3; }
  .md\:col-4 { grid-column: span 4; }
  .md\:col-6 { grid-column: span 6; }
  .md\:col-12 { grid-column: span 12; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
}

@media (min-width: 1024px) {
  .lg\:col-1 { grid-column: span 1; }
  .lg\:col-2 { grid-column: span 2; }
  .lg\:col-3 { grid-column: span 3; }
  .lg\:col-4 { grid-column: span 4; }
  .lg\:col-6 { grid-column: span 6; }
  .lg\:col-12 { grid-column: span 12; }
  .lg\:flex { display: flex; }
  .lg\:hidden { display: none; }
}

/* 辅助功能 */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
