/**
 * 设置持久化管理器
 * 提供高级的设置数据持久化和同步功能
 */

import { StorageUtils } from './storage.js';
import { EventManager } from './events.js';

/**
 * 设置持久化管理器类
 */
export class SettingsPersistenceManager {
  constructor() {
    this.eventManager = new EventManager();
    this.settings = {};
    this.watchers = new Map();
    this.debounceTimers = new Map();
    this.syncInProgress = false;
    
    // 设置版本管理
    this.currentVersion = '1.2.0';
    this.migrationHandlers = new Map();
    
    // 默认设置配置
    this.defaultSettings = {
      // 外观设置
      theme: 'auto',
      fontSize: 'md',
      
      // 聊天设置
      autoScroll: true,
      showTimestamps: true,
      enableNotifications: true,
      maxHistorySize: 1000,
      collapseThreshold: 5,
      enableMessageEdit: true,
      
      // 高级设置
      debugMode: false,
      performanceMonitoring: false,
      experimentalFeatures: false,
      
      // 内部设置
      _version: this.currentVersion,
      _lastModified: Date.now(),
      _syncEnabled: true
    };
    
    // 设置验证规则
    this.validationRules = {
      theme: ['auto', 'light', 'dark', 'high-contrast'],
      fontSize: ['xs', 'sm', 'md', 'lg', 'xl', '2xl'],
      maxHistorySize: (value) => Number.isInteger(value) && value >= 100 && value <= 10000,
      collapseThreshold: (value) => Number.isInteger(value) && value >= 0 && value <= 20
    };
    
    // 绑定方法上下文
    this.init = this.init.bind(this);
    this.get = this.get.bind(this);
    this.set = this.set.bind(this);
    this.watch = this.watch.bind(this);
    this.sync = this.sync.bind(this);
  }

  /**
   * 初始化持久化管理器
   */
  async init() {
    try {
      // 加载设置
      await this.loadSettings();
      
      // 执行数据迁移
      await this.migrateSettings();
      
      // 设置自动同步
      this.setupAutoSync();
      
      // 监听存储变化
      this.setupStorageListener();
      
      console.log('Settings persistence manager initialized');
      
    } catch (error) {
      console.error('Failed to initialize settings persistence:', error);
      // 使用默认设置
      this.settings = { ...this.defaultSettings };
    }
  }

  /**
   * 加载设置
   */
  async loadSettings() {
    const storedSettings = StorageUtils.getSettings();
    
    // 合并默认设置和存储的设置
    this.settings = {
      ...this.defaultSettings,
      ...storedSettings,
      _lastLoaded: Date.now()
    };
    
    // 验证设置
    this.validateSettings();
    
    this.eventManager.emit('settingsLoaded', this.settings);
  }

  /**
   * 验证设置
   */
  validateSettings() {
    const errors = [];
    
    Object.entries(this.validationRules).forEach(([key, rule]) => {
      const value = this.settings[key];
      
      if (Array.isArray(rule)) {
        if (!rule.includes(value)) {
          errors.push(`Invalid value for ${key}: ${value}`);
          this.settings[key] = this.defaultSettings[key];
        }
      } else if (typeof rule === 'function') {
        if (!rule(value)) {
          errors.push(`Invalid value for ${key}: ${value}`);
          this.settings[key] = this.defaultSettings[key];
        }
      }
    });
    
    if (errors.length > 0) {
      console.warn('Settings validation errors:', errors);
      this.eventManager.emit('settingsValidationErrors', errors);
    }
  }

  /**
   * 获取设置值
   * @param {string} key - 设置键
   * @param {any} defaultValue - 默认值
   * @returns {any} 设置值
   */
  get(key, defaultValue = undefined) {
    if (key === undefined) {
      return { ...this.settings };
    }
    
    return this.settings.hasOwnProperty(key) 
      ? this.settings[key] 
      : (defaultValue !== undefined ? defaultValue : this.defaultSettings[key]);
  }

  /**
   * 设置值
   * @param {string|object} key - 设置键或设置对象
   * @param {any} value - 设置值
   * @param {object} options - 选项
   */
  async set(key, value = undefined, options = {}) {
    const {
      sync = true,
      validate = true,
      notify = true,
      debounce = 300
    } = options;
    
    let changes = {};
    
    if (typeof key === 'object') {
      // 批量设置
      changes = key;
    } else {
      // 单个设置
      changes[key] = value;
    }
    
    // 验证变更
    if (validate) {
      changes = this.validateChanges(changes);
    }
    
    // 应用变更
    const oldSettings = { ...this.settings };
    Object.assign(this.settings, changes);
    this.settings._lastModified = Date.now();
    
    // 通知变更
    if (notify) {
      Object.entries(changes).forEach(([changedKey, newValue]) => {
        const oldValue = oldSettings[changedKey];
        if (oldValue !== newValue) {
          this.eventManager.emit('settingChanged', {
            key: changedKey,
            oldValue,
            newValue,
            timestamp: Date.now()
          });
        }
      });
    }
    
    // 同步到存储
    if (sync) {
      if (debounce > 0) {
        this.debouncedSync(debounce);
      } else {
        await this.sync();
      }
    }
  }

  /**
   * 验证变更
   * @param {object} changes - 变更对象
   * @returns {object} 验证后的变更
   */
  validateChanges(changes) {
    const validatedChanges = {};
    
    Object.entries(changes).forEach(([key, value]) => {
      const rule = this.validationRules[key];
      
      if (rule) {
        if (Array.isArray(rule)) {
          if (rule.includes(value)) {
            validatedChanges[key] = value;
          } else {
            console.warn(`Invalid value for ${key}: ${value}, using default`);
            validatedChanges[key] = this.defaultSettings[key];
          }
        } else if (typeof rule === 'function') {
          if (rule(value)) {
            validatedChanges[key] = value;
          } else {
            console.warn(`Invalid value for ${key}: ${value}, using default`);
            validatedChanges[key] = this.defaultSettings[key];
          }
        }
      } else {
        // 没有验证规则的设置直接通过
        validatedChanges[key] = value;
      }
    });
    
    return validatedChanges;
  }

  /**
   * 监听设置变化
   * @param {string|function} keyOrCallback - 设置键或回调函数
   * @param {function} callback - 回调函数
   * @returns {function} 取消监听的函数
   */
  watch(keyOrCallback, callback = null) {
    let watchKey, watchCallback;
    
    if (typeof keyOrCallback === 'function') {
      // 监听所有变化
      watchKey = '*';
      watchCallback = keyOrCallback;
    } else {
      // 监听特定键
      watchKey = keyOrCallback;
      watchCallback = callback;
    }
    
    if (!this.watchers.has(watchKey)) {
      this.watchers.set(watchKey, new Set());
    }
    
    this.watchers.get(watchKey).add(watchCallback);
    
    // 监听事件
    const eventHandler = (data) => {
      if (watchKey === '*' || data.key === watchKey) {
        watchCallback(data);
      }
    };
    
    this.eventManager.on('settingChanged', eventHandler);
    
    // 返回取消监听的函数
    return () => {
      this.watchers.get(watchKey)?.delete(watchCallback);
      this.eventManager.off('settingChanged', eventHandler);
    };
  }

  /**
   * 防抖同步
   * @param {number} delay - 延迟时间
   */
  debouncedSync(delay = 300) {
    const timerId = 'sync';
    
    if (this.debounceTimers.has(timerId)) {
      clearTimeout(this.debounceTimers.get(timerId));
    }
    
    const timer = setTimeout(() => {
      this.sync();
      this.debounceTimers.delete(timerId);
    }, delay);
    
    this.debounceTimers.set(timerId, timer);
  }

  /**
   * 同步到存储
   */
  async sync() {
    if (this.syncInProgress || !this.settings._syncEnabled) {
      return;
    }
    
    this.syncInProgress = true;
    
    try {
      const success = StorageUtils.saveSettings(this.settings);
      
      if (success) {
        this.eventManager.emit('settingsSynced', {
          timestamp: Date.now(),
          settings: { ...this.settings }
        });
      } else {
        throw new Error('Failed to save settings to storage');
      }
      
    } catch (error) {
      console.error('Settings sync failed:', error);
      this.eventManager.emit('settingsSyncError', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 设置自动同步
   */
  setupAutoSync() {
    // 页面卸载时同步
    window.addEventListener('beforeunload', () => {
      this.sync();
    });
    
    // 定期同步（可选）
    if (this.settings.autoSync !== false) {
      setInterval(() => {
        if (this.settings._lastModified > (this.settings._lastSynced || 0)) {
          this.sync();
        }
      }, 30000); // 30秒检查一次
    }
  }

  /**
   * 设置存储监听器
   */
  setupStorageListener() {
    window.addEventListener('storage', (e) => {
      if (e.key === StorageUtils.STORAGE_KEYS.SETTINGS) {
        // 其他标签页修改了设置，重新加载
        this.loadSettings();
      }
    });
  }

  /**
   * 数据迁移
   */
  async migrateSettings() {
    const currentVersion = this.settings._version || '1.0.0';
    
    if (currentVersion !== this.currentVersion) {
      console.log(`Migrating settings from ${currentVersion} to ${this.currentVersion}`);
      
      // 执行迁移
      for (const [version, handler] of this.migrationHandlers) {
        if (this.compareVersions(currentVersion, version) < 0) {
          await handler(this.settings);
        }
      }
      
      // 更新版本
      this.settings._version = this.currentVersion;
      await this.sync();
    }
  }

  /**
   * 比较版本号
   * @param {string} v1 - 版本1
   * @param {string} v2 - 版本2
   * @returns {number} 比较结果
   */
  compareVersions(v1, v2) {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const part1 = parts1[i] || 0;
      const part2 = parts2[i] || 0;
      
      if (part1 < part2) return -1;
      if (part1 > part2) return 1;
    }
    
    return 0;
  }

  /**
   * 重置设置
   * @param {Array} keys - 要重置的键，不传则重置所有
   */
  async reset(keys = null) {
    if (keys) {
      // 重置指定键
      const changes = {};
      keys.forEach(key => {
        if (this.defaultSettings.hasOwnProperty(key)) {
          changes[key] = this.defaultSettings[key];
        }
      });
      await this.set(changes);
    } else {
      // 重置所有设置
      this.settings = { ...this.defaultSettings };
      this.settings._lastModified = Date.now();
      await this.sync();
      
      this.eventManager.emit('settingsReset', {
        timestamp: Date.now()
      });
    }
  }

  /**
   * 导出设置
   * @returns {object} 导出的设置
   */
  export() {
    return {
      settings: { ...this.settings },
      exportTime: new Date().toISOString(),
      version: this.currentVersion
    };
  }

  /**
   * 导入设置
   * @param {object} data - 导入的数据
   */
  async import(data) {
    if (data.settings) {
      await this.set(data.settings, undefined, { validate: true });
      
      this.eventManager.emit('settingsImported', {
        timestamp: Date.now(),
        importedVersion: data.version
      });
    }
  }

  /**
   * 获取事件管理器
   * @returns {EventManager} 事件管理器
   */
  getEventManager() {
    return this.eventManager;
  }

  /**
   * 销毁管理器
   */
  destroy() {
    // 清理定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
    
    // 清理监听器
    this.watchers.clear();
    
    // 清理事件
    this.eventManager.removeAllListeners();
  }
}

// 创建全局实例
export const globalSettingsPersistence = new SettingsPersistenceManager();

export default SettingsPersistenceManager;
