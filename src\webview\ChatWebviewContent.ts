import * as vscode from "vscode";
import * as fs from "fs";
import * as path from "path";

/**
 * 管理聊天 WebView 的 HTML 内容
 */
export class ChatWebviewContent {
    /**
     * 生成聊天 WebView 的 HTML 内容
     * @param webview WebView 实例
     * @param extensionUri 扩展URI
     * @returns HTML 字符串
     */
    public static getHtmlForWebview(webview: vscode.Webview, extensionUri: vscode.Uri): string {
        try {
            // 读取HTML模板文件
            const htmlTemplatePath = path.join(extensionUri.fsPath, "src", "webview", "chat.html");
            let htmlContent = fs.readFileSync(htmlTemplatePath, "utf8");

            const onDiskPath = vscode.Uri.joinPath(extensionUri);
            const webviewUri = webview.asWebviewUri(onDiskPath);
            // 替换占位符
            htmlContent = htmlContent.replace(/\{\{CSP_SOURCE\}\}/g, webview.cspSource);
            htmlContent = htmlContent.replace(/(<link.+?href="|<script.+?src="|<iframe.+?src="|<img.+?src=")(.+?)"/g, (m, $1, $2) => {
                if ($2.indexOf("https://") < 0) {
                    return $1 + webviewUri.toString() + $2 + '"';
                }
                else {
                    return $1 + $2 + '"';
                }
            });
            // 替换 ES6 模块 import 语句中的路径
            htmlContent = htmlContent.replace(/(import\s+.+?\s+from\s+['"])(\/.+?)(['"])/g, (_m, $1, $2, $3) => {
                if ($2.indexOf("https://") < 0) {
                    return $1 + webviewUri.toString() + $2 + $3;
                }
                else {
                    return $1 + $2 + $3;
                }
            });
            return htmlContent;
        } catch (error) {
            console.error("读取HTML模板文件失败:", error);
            // 如果读取失败，返回一个简单的错误页面
            return this.getFallbackHtml(webview);
        }
    }

    /**
     * 获取备用HTML内容（当模板文件读取失败时使用）
     * @param webview WebView 实例
     * @returns 备用HTML字符串
     */
    private static getFallbackHtml(webview: vscode.Webview): string {
        return `
            <!DOCTYPE html>
            <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta http-equiv="Content-Security-Policy" content="default-src '"'"'none'"'"'; style-src ${webview.cspSource} '"'unsafe-inline'"'"'; script-src ${webview.cspSource} '"'"'unsafe-inline'"'"';">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>AI 聊天</title>
                    <style>
                        body {
                            font-family: var(--vscode-font-family);
                            padding: 20px;
                            background-color: var(--vscode-sideBar-background);
                            color: var(--vscode-foreground);
                            text-align: center;
                        }
                        .error-message {
                            color: var(--vscode-errorForeground);
                            margin-top: 20px;
                        }
                    </style>
                </head>
                <body>
                    <h2>AI 聊天</h2>
                    <div class="error-message">
                        <p>抱歉，聊天界面加载失败。</p>
                        <p>请尝试重新加载扩展。</p>
                    </div>
                </body>
            </html>`;
    }
}