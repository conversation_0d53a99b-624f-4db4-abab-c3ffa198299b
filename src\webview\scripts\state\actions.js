/**
 * 状态操作定义
 * 提供标准化的状态更新操作
 */

/**
 * 动作类型常量
 */
export const ActionTypes = {
  // 设置相关
  SET_THEME: 'SET_THEME',
  SET_FONT_SIZE: 'SET_FONT_SIZE',
  SET_AUTO_SCROLL: 'SET_AUTO_SCROLL',
  SET_SHOW_TIMESTAMPS: 'SET_SHOW_TIMESTAMPS',
  SET_ENABLE_NOTIFICATIONS: 'SET_ENABLE_NOTIFICATIONS',
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',
  RESET_SETTINGS: 'RESET_SETTINGS',
  
  // 消息相关
  ADD_MESSAGE: 'ADD_MESSAGE',
  UPDATE_MESSAGE: 'UPDATE_MESSAGE',
  DELETE_MESSAGE: 'DELETE_MESSAGE',
  CLEAR_MESSAGES: 'CLEAR_MESSAGES',
  SET_MESSAGES: 'SET_MESSAGES',
  
  // UI状态相关
  SET_LOADING: 'SET_LOADING',
  SET_CURRENT_VIEW: 'SET_CURRENT_VIEW',
  SET_INPUT_VALUE: 'SET_INPUT_VALUE',
  SET_TYPING: 'SET_TYPING',
  UPDATE_UI: 'UPDATE_UI',
  
  // 用户相关
  SET_USER_INFO: 'SET_USER_INFO',
  
  // 应用状态相关
  SET_INITIALIZED: 'SET_INITIALIZED',
  UPDATE_LAST_ACTIVITY: 'UPDATE_LAST_ACTIVITY'
};

/**
 * 动作创建器类
 */
export class ActionCreators {
  /**
   * 设置主题
   * @param {string} theme - 主题名称
   * @returns {object} 动作对象
   */
  static setTheme(theme) {
    return {
      type: ActionTypes.SET_THEME,
      payload: { theme }
    };
  }

  /**
   * 设置字体大小
   * @param {string} fontSize - 字体大小
   * @returns {object} 动作对象
   */
  static setFontSize(fontSize) {
    return {
      type: ActionTypes.SET_FONT_SIZE,
      payload: { fontSize }
    };
  }

  /**
   * 设置自动滚动
   * @param {boolean} autoScroll - 是否自动滚动
   * @returns {object} 动作对象
   */
  static setAutoScroll(autoScroll) {
    return {
      type: ActionTypes.SET_AUTO_SCROLL,
      payload: { autoScroll }
    };
  }

  /**
   * 设置显示时间戳
   * @param {boolean} showTimestamps - 是否显示时间戳
   * @returns {object} 动作对象
   */
  static setShowTimestamps(showTimestamps) {
    return {
      type: ActionTypes.SET_SHOW_TIMESTAMPS,
      payload: { showTimestamps }
    };
  }

  /**
   * 设置启用通知
   * @param {boolean} enableNotifications - 是否启用通知
   * @returns {object} 动作对象
   */
  static setEnableNotifications(enableNotifications) {
    return {
      type: ActionTypes.SET_ENABLE_NOTIFICATIONS,
      payload: { enableNotifications }
    };
  }

  /**
   * 更新设置
   * @param {object} settings - 设置对象
   * @returns {object} 动作对象
   */
  static updateSettings(settings) {
    return {
      type: ActionTypes.UPDATE_SETTINGS,
      payload: { settings }
    };
  }

  /**
   * 重置设置
   * @returns {object} 动作对象
   */
  static resetSettings() {
    return {
      type: ActionTypes.RESET_SETTINGS,
      payload: {}
    };
  }

  /**
   * 添加消息
   * @param {object} message - 消息对象
   * @returns {object} 动作对象
   */
  static addMessage(message) {
    return {
      type: ActionTypes.ADD_MESSAGE,
      payload: { message }
    };
  }

  /**
   * 更新消息
   * @param {string} messageId - 消息ID
   * @param {object} updates - 更新内容
   * @returns {object} 动作对象
   */
  static updateMessage(messageId, updates) {
    return {
      type: ActionTypes.UPDATE_MESSAGE,
      payload: { messageId, updates }
    };
  }

  /**
   * 删除消息
   * @param {string} messageId - 消息ID
   * @returns {object} 动作对象
   */
  static deleteMessage(messageId) {
    return {
      type: ActionTypes.DELETE_MESSAGE,
      payload: { messageId }
    };
  }

  /**
   * 清空消息
   * @returns {object} 动作对象
   */
  static clearMessages() {
    return {
      type: ActionTypes.CLEAR_MESSAGES,
      payload: {}
    };
  }

  /**
   * 设置消息列表
   * @param {Array} messages - 消息数组
   * @returns {object} 动作对象
   */
  static setMessages(messages) {
    return {
      type: ActionTypes.SET_MESSAGES,
      payload: { messages }
    };
  }

  /**
   * 设置加载状态
   * @param {boolean} isLoading - 是否加载中
   * @returns {object} 动作对象
   */
  static setLoading(isLoading) {
    return {
      type: ActionTypes.SET_LOADING,
      payload: { isLoading }
    };
  }

  /**
   * 设置当前视图
   * @param {string} currentView - 当前视图
   * @returns {object} 动作对象
   */
  static setCurrentView(currentView) {
    return {
      type: ActionTypes.SET_CURRENT_VIEW,
      payload: { currentView }
    };
  }

  /**
   * 设置输入值
   * @param {string} inputValue - 输入值
   * @returns {object} 动作对象
   */
  static setInputValue(inputValue) {
    return {
      type: ActionTypes.SET_INPUT_VALUE,
      payload: { inputValue }
    };
  }

  /**
   * 设置输入状态
   * @param {boolean} isTyping - 是否正在输入
   * @returns {object} 动作对象
   */
  static setTyping(isTyping) {
    return {
      type: ActionTypes.SET_TYPING,
      payload: { isTyping }
    };
  }

  /**
   * 更新UI状态
   * @param {object} uiUpdates - UI更新对象
   * @returns {object} 动作对象
   */
  static updateUI(uiUpdates) {
    return {
      type: ActionTypes.UPDATE_UI,
      payload: { uiUpdates }
    };
  }

  /**
   * 设置用户信息
   * @param {object} userInfo - 用户信息
   * @returns {object} 动作对象
   */
  static setUserInfo(userInfo) {
    return {
      type: ActionTypes.SET_USER_INFO,
      payload: { userInfo }
    };
  }

  /**
   * 设置初始化状态
   * @param {boolean} initialized - 是否已初始化
   * @returns {object} 动作对象
   */
  static setInitialized(initialized) {
    return {
      type: ActionTypes.SET_INITIALIZED,
      payload: { initialized }
    };
  }

  /**
   * 更新最后活动时间
   * @returns {object} 动作对象
   */
  static updateLastActivity() {
    return {
      type: ActionTypes.UPDATE_LAST_ACTIVITY,
      payload: { timestamp: Date.now() }
    };
  }
}

/**
 * 状态减速器（Reducer）
 * 处理状态更新逻辑
 */
export class StateReducer {
  /**
   * 处理设置相关的状态更新
   * @param {object} state - 当前状态
   * @param {object} action - 动作对象
   * @returns {object} 新状态
   */
  static handleSettingsActions(state, action) {
    const { type, payload } = action;
    
    switch (type) {
      case ActionTypes.SET_THEME:
        return {
          ...state,
          settings: {
            ...state.settings,
            theme: payload.theme
          }
        };
        
      case ActionTypes.SET_FONT_SIZE:
        return {
          ...state,
          settings: {
            ...state.settings,
            fontSize: payload.fontSize
          }
        };
        
      case ActionTypes.SET_AUTO_SCROLL:
        return {
          ...state,
          settings: {
            ...state.settings,
            autoScroll: payload.autoScroll
          }
        };
        
      case ActionTypes.SET_SHOW_TIMESTAMPS:
        return {
          ...state,
          settings: {
            ...state.settings,
            showTimestamps: payload.showTimestamps
          }
        };
        
      case ActionTypes.SET_ENABLE_NOTIFICATIONS:
        return {
          ...state,
          settings: {
            ...state.settings,
            enableNotifications: payload.enableNotifications
          }
        };
        
      case ActionTypes.UPDATE_SETTINGS:
        return {
          ...state,
          settings: {
            ...state.settings,
            ...payload.settings
          }
        };
        
      case ActionTypes.RESET_SETTINGS:
        return {
          ...state,
          settings: {
            theme: 'auto',
            fontSize: 'md',
            autoScroll: true,
            showTimestamps: true,
            enableNotifications: true
          }
        };
        
      default:
        return state;
    }
  }

  /**
   * 处理消息相关的状态更新
   * @param {object} state - 当前状态
   * @param {object} action - 动作对象
   * @returns {object} 新状态
   */
  static handleMessageActions(state, action) {
    const { type, payload } = action;
    
    switch (type) {
      case ActionTypes.ADD_MESSAGE:
        return {
          ...state,
          messages: [...state.messages, payload.message]
        };
        
      case ActionTypes.UPDATE_MESSAGE:
        return {
          ...state,
          messages: state.messages.map(msg =>
            msg.id === payload.messageId
              ? { ...msg, ...payload.updates }
              : msg
          )
        };
        
      case ActionTypes.DELETE_MESSAGE:
        return {
          ...state,
          messages: state.messages.filter(msg => msg.id !== payload.messageId)
        };
        
      case ActionTypes.CLEAR_MESSAGES:
        return {
          ...state,
          messages: []
        };
        
      case ActionTypes.SET_MESSAGES:
        return {
          ...state,
          messages: payload.messages
        };
        
      default:
        return state;
    }
  }

  /**
   * 处理UI相关的状态更新
   * @param {object} state - 当前状态
   * @param {object} action - 动作对象
   * @returns {object} 新状态
   */
  static handleUIActions(state, action) {
    const { type, payload } = action;
    
    switch (type) {
      case ActionTypes.SET_LOADING:
        return {
          ...state,
          ui: {
            ...state.ui,
            isLoading: payload.isLoading
          }
        };
        
      case ActionTypes.SET_CURRENT_VIEW:
        return {
          ...state,
          ui: {
            ...state.ui,
            currentView: payload.currentView
          }
        };
        
      case ActionTypes.SET_INPUT_VALUE:
        return {
          ...state,
          ui: {
            ...state.ui,
            inputValue: payload.inputValue
          }
        };
        
      case ActionTypes.SET_TYPING:
        return {
          ...state,
          ui: {
            ...state.ui,
            isTyping: payload.isTyping
          }
        };
        
      case ActionTypes.UPDATE_UI:
        return {
          ...state,
          ui: {
            ...state.ui,
            ...payload.uiUpdates
          }
        };
        
      default:
        return state;
    }
  }

  /**
   * 主减速器
   * @param {object} state - 当前状态
   * @param {object} action - 动作对象
   * @returns {object} 新状态
   */
  static reduce(state, action) {
    // 尝试各个子减速器
    let newState = this.handleSettingsActions(state, action);
    if (newState !== state) return newState;
    
    newState = this.handleMessageActions(state, action);
    if (newState !== state) return newState;
    
    newState = this.handleUIActions(state, action);
    if (newState !== state) return newState;
    
    // 处理其他动作
    const { type, payload } = action;
    
    switch (type) {
      case ActionTypes.SET_USER_INFO:
        return {
          ...state,
          user: {
            ...state.user,
            ...payload.userInfo
          }
        };
        
      case ActionTypes.SET_INITIALIZED:
        return {
          ...state,
          app: {
            ...state.app,
            initialized: payload.initialized
          }
        };
        
      case ActionTypes.UPDATE_LAST_ACTIVITY:
        return {
          ...state,
          app: {
            ...state.app,
            lastActivity: payload.timestamp
          }
        };
        
      default:
        return state;
    }
  }
}

// 导出默认动作创建器
export default ActionCreators;
