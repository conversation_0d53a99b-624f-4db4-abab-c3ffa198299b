<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disabled属性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .btn {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
        }
        
        .btn:hover:not(:disabled) {
            background: #e6e6e6;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f5f5f5;
        }
        
        .log {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status.enabled {
            background: #d4edda;
            color: #155724;
        }
        
        .status.disabled {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Disabled属性测试</h1>
    <p>测试HTML中disabled属性的正确处理方式</p>
    
    <div class="test-section">
        <h3>问题演示：错误的disabled属性处理</h3>
        <p>这些按钮展示了错误的disabled属性处理方式：</p>
        
        <button class="btn" id="wrong-btn-1">正常按钮</button>
        <button class="btn" id="wrong-btn-2" disabled="false">disabled="false" (仍然被禁用!)</button>
        <button class="btn" id="wrong-btn-3" disabled="">disabled="" (仍然被禁用!)</button>
        <button class="btn" id="wrong-btn-4" disabled="disabled">disabled="disabled" (正确禁用)</button>
        
        <div class="log" id="wrong-log">点击按钮查看状态...</div>
        
        <button class="btn" onclick="testWrongButtons()">测试错误的按钮</button>
    </div>
    
    <div class="test-section">
        <h3>正确的disabled属性处理</h3>
        <p>这些按钮展示了正确的disabled属性处理方式：</p>
        
        <button class="btn" id="correct-btn-1">启用按钮</button>
        <button class="btn" id="correct-btn-2">可切换按钮</button>
        
        <div class="log" id="correct-log">点击按钮查看状态...</div>
        
        <button class="btn" onclick="toggleCorrectButton()">切换按钮状态</button>
        <button class="btn" onclick="testCorrectButtons()">测试正确的按钮</button>
    </div>
    
    <div class="test-section">
        <h3>版本控制按钮模拟</h3>
        <p>模拟消息版本控制按钮的行为：</p>
        
        <div style="display: flex; align-items: center; gap: 10px; margin: 10px 0;">
            <button class="btn" id="prev-version" onclick="switchVersion(-1)">◀ 上一个</button>
            <span id="version-display">版本 1 / 1</span>
            <button class="btn" id="next-version" onclick="switchVersion(1)">下一个 ▶</button>
        </div>
        
        <div class="log" id="version-log">版本控制日志...</div>
        
        <button class="btn" onclick="addVersion()">添加新版本</button>
        <button class="btn" onclick="resetVersions()">重置版本</button>
    </div>
    
    <script>
        // 全局变量
        window.versions = ['版本1内容'];
        window.currentVersionIndex = 0;
        
        // 日志函数
        function log(containerId, message) {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            container.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            container.scrollTop = container.scrollHeight;
        }
        
        // 测试错误的按钮处理
        function testWrongButtons() {
            const buttons = [
                { id: 'wrong-btn-1', name: '正常按钮' },
                { id: 'wrong-btn-2', name: 'disabled="false"' },
                { id: 'wrong-btn-3', name: 'disabled=""' },
                { id: 'wrong-btn-4', name: 'disabled="disabled"' }
            ];
            
            log('wrong-log', '=== 测试错误的按钮处理 ===');
            
            buttons.forEach(({ id, name }) => {
                const btn = document.getElementById(id);
                const isDisabled = btn.disabled;
                const hasAttribute = btn.hasAttribute('disabled');
                const attributeValue = btn.getAttribute('disabled');
                
                log('wrong-log', `${name}:`);
                log('wrong-log', `  - disabled属性: ${isDisabled}`);
                log('wrong-log', `  - 有disabled属性: ${hasAttribute}`);
                log('wrong-log', `  - 属性值: "${attributeValue}"`);
                log('wrong-log', `  - 可点击: ${!isDisabled ? '是' : '否'}`);
                log('wrong-log', '');
            });
        }
        
        // 切换正确按钮的状态
        function toggleCorrectButton() {
            const btn = document.getElementById('correct-btn-2');
            const shouldDisable = !btn.disabled;
            
            log('correct-log', `切换按钮状态: ${shouldDisable ? '禁用' : '启用'}`);
            
            if (shouldDisable) {
                btn.disabled = true;
                btn.setAttribute('disabled', 'disabled');
                log('correct-log', '设置: disabled=true, setAttribute("disabled", "disabled")');
            } else {
                btn.disabled = false;
                btn.removeAttribute('disabled');
                log('correct-log', '设置: disabled=false, removeAttribute("disabled")');
            }
            
            // 验证状态
            log('correct-log', `结果: disabled=${btn.disabled}, hasAttribute=${btn.hasAttribute('disabled')}`);
            log('correct-log', '');
        }
        
        // 测试正确的按钮处理
        function testCorrectButtons() {
            const buttons = [
                { id: 'correct-btn-1', name: '启用按钮' },
                { id: 'correct-btn-2', name: '可切换按钮' }
            ];
            
            log('correct-log', '=== 测试正确的按钮处理 ===');
            
            buttons.forEach(({ id, name }) => {
                const btn = document.getElementById(id);
                const isDisabled = btn.disabled;
                const hasAttribute = btn.hasAttribute('disabled');
                const attributeValue = btn.getAttribute('disabled');
                
                log('correct-log', `${name}:`);
                log('correct-log', `  - disabled属性: ${isDisabled}`);
                log('correct-log', `  - 有disabled属性: ${hasAttribute}`);
                log('correct-log', `  - 属性值: "${attributeValue}"`);
                log('correct-log', `  - 可点击: ${!isDisabled ? '是' : '否'}`);
                log('correct-log', '');
            });
        }
        
        // 版本控制函数
        function updateVersionControls() {
            const prevBtn = document.getElementById('prev-version');
            const nextBtn = document.getElementById('next-version');
            const display = document.getElementById('version-display');
            
            // 更新显示
            display.textContent = `版本 ${window.currentVersionIndex + 1} / ${window.versions.length}`;
            
            // 正确处理disabled属性
            const shouldDisablePrev = window.currentVersionIndex === 0;
            const shouldDisableNext = window.currentVersionIndex === window.versions.length - 1;
            
            // 上一个按钮
            prevBtn.disabled = shouldDisablePrev;
            if (shouldDisablePrev) {
                prevBtn.setAttribute('disabled', 'disabled');
            } else {
                prevBtn.removeAttribute('disabled');
            }
            
            // 下一个按钮
            nextBtn.disabled = shouldDisableNext;
            if (shouldDisableNext) {
                nextBtn.setAttribute('disabled', 'disabled');
            } else {
                nextBtn.removeAttribute('disabled');
            }
            
            // 记录状态
            log('version-log', `版本控制更新: ${display.textContent}`);
            log('version-log', `上一个按钮: disabled=${prevBtn.disabled}, hasAttr=${prevBtn.hasAttribute('disabled')}`);
            log('version-log', `下一个按钮: disabled=${nextBtn.disabled}, hasAttr=${nextBtn.hasAttribute('disabled')}`);
            log('version-log', '');
        }
        
        function switchVersion(direction) {
            const newIndex = window.currentVersionIndex + direction;
            
            if (newIndex < 0 || newIndex >= window.versions.length) {
                log('version-log', `无法切换到版本 ${newIndex + 1} (超出范围)`);
                return;
            }
            
            window.currentVersionIndex = newIndex;
            log('version-log', `切换到版本 ${newIndex + 1}: ${window.versions[newIndex]}`);
            updateVersionControls();
        }
        
        function addVersion() {
            const newVersion = `版本${window.versions.length + 1}内容`;
            window.versions.push(newVersion);
            window.currentVersionIndex = window.versions.length - 1;
            
            log('version-log', `添加新版本: ${newVersion}`);
            updateVersionControls();
        }
        
        function resetVersions() {
            window.versions = ['版本1内容'];
            window.currentVersionIndex = 0;
            
            log('version-log', '重置版本列表');
            updateVersionControls();
        }
        
        // 为按钮添加点击事件来测试是否真的可点击
        document.getElementById('wrong-btn-1').addEventListener('click', () => log('wrong-log', '正常按钮被点击!'));
        document.getElementById('wrong-btn-2').addEventListener('click', () => log('wrong-log', 'disabled="false"按钮被点击! (不应该发生)'));
        document.getElementById('wrong-btn-3').addEventListener('click', () => log('wrong-log', 'disabled=""按钮被点击! (不应该发生)'));
        document.getElementById('wrong-btn-4').addEventListener('click', () => log('wrong-log', 'disabled="disabled"按钮被点击! (不应该发生)'));
        
        document.getElementById('correct-btn-1').addEventListener('click', () => log('correct-log', '启用按钮被点击!'));
        document.getElementById('correct-btn-2').addEventListener('click', () => log('correct-log', '可切换按钮被点击!'));
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('version-log', '页面加载完成，初始化版本控制');
            updateVersionControls();
        });
    </script>
</body>
</html>
