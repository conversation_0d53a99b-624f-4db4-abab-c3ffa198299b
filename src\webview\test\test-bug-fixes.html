<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bug修复测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="../styles/variables.css">
    <link rel="stylesheet" href="../styles/components.css">
    <link rel="stylesheet" href="../styles/layout.css">
    <link rel="stylesheet" href="../styles/themes.css">
    
    <style>
        .test-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--color-border);
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: var(--color-primary);
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: var(--color-success);
            color: white;
        }
        
        .status.error {
            background-color: var(--color-error);
            color: white;
        }
        
        .status.pending {
            background-color: var(--color-warning);
            color: white;
        }
        
        .message-test {
            border: 1px solid var(--color-border);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        
        .version-controls-test {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Bug修复测试页面</h1>
        <p>测试三个已修复的问题：</p>
        
        <!-- 测试1：版本切换按钮 -->
        <div class="test-section">
            <h3>测试1：消息版本切换按钮 <span id="test1-status" class="status pending">待测试</span></h3>
            <p>测试版本切换按钮是否正确启用/禁用</p>
            
            <div class="message-test" id="test-message">
                <div class="message-content">
                    <div class="message-text">这是一个测试消息，用于测试版本控制功能。</div>
                    <div class="message-actions">
                        <div class="message-version-controls">
                            <button class="btn btn-icon version-btn" id="prev-btn" title="上一个版本">◀</button>
                            <span class="version-info" id="version-info">1 / 1</span>
                            <button class="btn btn-icon version-btn" id="next-btn" title="下一个版本">▶</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="addVersion()">添加新版本</button>
                <button class="btn btn-secondary" onclick="testVersionSwitch()">测试版本切换</button>
                <button class="btn btn-secondary" onclick="resetVersionTest()">重置测试</button>
            </div>
        </div>
        
        <!-- 测试2：AI回复生成 -->
        <div class="test-section">
            <h3>测试2：消息编辑后AI回复 <span id="test2-status" class="status pending">待测试</span></h3>
            <p>测试编辑用户消息后是否能正确触发AI回复</p>
            
            <div id="messages-test-container">
                <!-- 消息将在这里显示 -->
            </div>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="addTestMessage()">添加测试消息</button>
                <button class="btn btn-secondary" onclick="editLastMessage()">编辑最后一条消息</button>
                <button class="btn btn-secondary" onclick="clearTestMessages()">清空消息</button>
            </div>
        </div>
        
        <!-- 测试3：通知定位 -->
        <div class="test-section">
            <h3>测试3：通知右侧定位 <span id="test3-status" class="status pending">待测试</span></h3>
            <p>测试通知是否正确显示在右上角</p>
            
            <div class="test-buttons">
                <button class="btn btn-success" onclick="showSuccessNotification()">显示成功通知</button>
                <button class="btn btn-warning" onclick="showWarningNotification()">显示警告通知</button>
                <button class="btn btn-error" onclick="showErrorNotification()">显示错误通知</button>
                <button class="btn btn-info" onclick="showInfoNotification()">显示信息通知</button>
                <button class="btn btn-secondary" onclick="clearAllNotifications()">清空所有通知</button>
            </div>
        </div>
        
        <!-- 测试结果 -->
        <div class="test-section">
            <h3>测试结果</h3>
            <div id="test-results">
                <p>等待测试结果...</p>
            </div>
        </div>
    </div>
    
    <!-- Toast通知容器 -->
    <div class="toast-container" id="toastContainer">
        <!-- Toast通知将在这里显示 -->
    </div>
    
    <script type="module">
        import { globalNotificationManager, NotificationTypes } from '../scripts/components/notification-manager.js';
        import { globalToolbarNotifications, ToolbarActions } from '../scripts/components/toolbar-notifications.js';
        import { EventManager } from '../scripts/utils/events.js';
        
        // 全局变量
        window.notificationManager = globalNotificationManager;
        window.toolbarNotifications = globalToolbarNotifications;
        window.eventManager = new EventManager();
        window.testMessages = [];
        window.testVersions = [
            { content: "这是一个测试消息，用于测试版本控制功能。", timestamp: Date.now() }
        ];
        window.currentVersionIndex = 0;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                await globalNotificationManager.init();
                console.log('Bug修复测试页面初始化完成');
                
                // 设置事件监听
                setupEventListeners();
                
                // 初始化版本控件状态
                updateVersionControls();
                
            } catch (error) {
                console.error('初始化失败:', error);
            }
        });
        
        // 设置事件监听
        function setupEventListeners() {
            // 监听AI回复重新生成事件
            window.eventManager.on('message:regenerate-ai-reply', (data) => {
                console.log('收到AI回复重新生成事件:', data);
                updateTestStatus('test2', 'success', '✓ 事件正确触发');
                
                // 模拟AI回复
                setTimeout(() => {
                    addAIReply('这是重新生成的AI回复：' + data.content);
                }, 1000);
            });
        }
        
        // 测试函数
        window.addVersion = function() {
            const newVersion = {
                content: `版本 ${window.testVersions.length + 1} 的内容 - ${new Date().toLocaleTimeString()}`,
                timestamp: Date.now()
            };
            window.testVersions.push(newVersion);
            window.currentVersionIndex = window.testVersions.length - 1;
            
            updateVersionControls();
            updateTestStatus('test1', 'success', '✓ 版本添加成功');
        };
        
        window.testVersionSwitch = function() {
            if (window.testVersions.length < 2) {
                updateTestStatus('test1', 'error', '✗ 需要至少2个版本才能测试切换');
                return;
            }
            
            // 测试切换到上一个版本
            if (window.currentVersionIndex > 0) {
                window.currentVersionIndex--;
                updateVersionControls();
                updateTestStatus('test1', 'success', '✓ 版本切换功能正常');
            }
        };
        
        window.resetVersionTest = function() {
            window.testVersions = [
                { content: "这是一个测试消息，用于测试版本控制功能。", timestamp: Date.now() }
            ];
            window.currentVersionIndex = 0;
            updateVersionControls();
            updateTestStatus('test1', 'pending', '待测试');
        };
        
        function updateVersionControls() {
            const versionInfo = document.getElementById('version-info');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const messageText = document.querySelector('.message-text');
            
            if (versionInfo) {
                versionInfo.textContent = `${window.currentVersionIndex + 1} / ${window.testVersions.length}`;
            }
            
            if (prevBtn) {
                prevBtn.disabled = window.currentVersionIndex === 0;
                if (prevBtn.disabled) {
                    prevBtn.setAttribute('disabled', 'disabled');
                } else {
                    prevBtn.removeAttribute('disabled');
                }
            }
            
            if (nextBtn) {
                nextBtn.disabled = window.currentVersionIndex === window.testVersions.length - 1;
                if (nextBtn.disabled) {
                    nextBtn.setAttribute('disabled', 'disabled');
                } else {
                    nextBtn.removeAttribute('disabled');
                }
            }
            
            if (messageText) {
                messageText.textContent = window.testVersions[window.currentVersionIndex].content;
            }
        }
        
        window.addTestMessage = function() {
            const container = document.getElementById('messages-test-container');
            const messageId = 'test-msg-' + Date.now();
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-user';
            messageDiv.dataset.messageId = messageId;
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-text">这是一条测试用户消息</div>
                    <div class="message-actions">
                        <button class="btn btn-icon message-edit-btn" onclick="editMessage('${messageId}')">✏️</button>
                    </div>
                </div>
                <div class="message-time">${new Date().toLocaleTimeString()}</div>
            `;
            
            container.appendChild(messageDiv);
            window.testMessages.push({ id: messageId, content: '这是一条测试用户消息', isUser: true });
        };
        
        window.editMessage = function(messageId) {
            // 模拟消息编辑后触发AI回复重新生成
            window.eventManager.emit('message:regenerate-ai-reply', {
                userMessage: { id: messageId },
                content: '这是编辑后的消息内容'
            });
        };
        
        window.editLastMessage = function() {
            if (window.testMessages.length === 0) {
                updateTestStatus('test2', 'error', '✗ 没有消息可编辑');
                return;
            }
            
            const lastMessage = window.testMessages[window.testMessages.length - 1];
            window.editMessage(lastMessage.id);
        };
        
        function addAIReply(content) {
            const container = document.getElementById('messages-test-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-ai';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-text">${content}</div>
                </div>
                <div class="message-time">${new Date().toLocaleTimeString()}</div>
            `;
            
            container.appendChild(messageDiv);
        }
        
        window.clearTestMessages = function() {
            document.getElementById('messages-test-container').innerHTML = '';
            window.testMessages = [];
            updateTestStatus('test2', 'pending', '待测试');
        };
        
        // 通知测试函数
        window.showSuccessNotification = function() {
            globalToolbarNotifications.notifySuccess(ToolbarActions.SAVE, '这是一个成功通知测试');
            updateTestStatus('test3', 'success', '✓ 通知显示正常');
        };
        
        window.showWarningNotification = function() {
            globalToolbarNotifications.notifyWarning(ToolbarActions.EDIT, '这是一个警告通知测试');
        };
        
        window.showErrorNotification = function() {
            globalToolbarNotifications.notifyError(ToolbarActions.DELETE, '这是一个错误通知测试');
        };
        
        window.showInfoNotification = function() {
            globalToolbarNotifications.notifyInfo(ToolbarActions.SEARCH, '这是一个信息通知测试');
        };
        
        window.clearAllNotifications = function() {
            // 清空所有通知
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(notification => {
                notification.remove();
            });
        };
        
        function updateTestStatus(testId, status, message) {
            const statusElement = document.getElementById(testId + '-status');
            if (statusElement) {
                statusElement.className = `status ${status}`;
                statusElement.textContent = message;
            }
        }
        
        // 版本切换按钮事件
        document.getElementById('prev-btn').addEventListener('click', () => {
            if (window.currentVersionIndex > 0) {
                window.currentVersionIndex--;
                updateVersionControls();
            }
        });
        
        document.getElementById('next-btn').addEventListener('click', () => {
            if (window.currentVersionIndex < window.testVersions.length - 1) {
                window.currentVersionIndex++;
                updateVersionControls();
            }
        });
    </script>
</body>
</html>
