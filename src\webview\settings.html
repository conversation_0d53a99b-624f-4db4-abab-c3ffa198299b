<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src {{CSP_SOURCE}} 'unsafe-inline'; script-src {{CSP_SOURCE}} 'unsafe-inline';">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 聊天 - 设置</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
</head>
<body>
    <div class="app-container" id="app">
        <!-- 设置页面 -->
        <div class="settings-container" id="settingsView">
            <!-- 设置内容将由JavaScript动态生成 -->
        </div>
        
        <!-- Toast通知容器 -->
        <div class="toast-container" id="toastContainer">
            <!-- Toast通知将在这里显示 -->
        </div>
    </div>
    
    <!-- 导入模块化JavaScript -->
    <script type="module">
        // 导入设置应用
        import SettingsApp from './scripts/settings-app.js';
        
        // 应用已在settings-app.js中自动初始化
        console.log('Settings application loaded');
    </script>
    
    <script>
        // 兼容性脚本 - 为不支持ES6模块的环境提供降级支持
        if (!window.settingsApp) {
            console.warn('ES6 modules not supported, loading fallback script');
            // 这里可以加载编译后的兼容性版本
        }
    </script>
</body>
</html>
