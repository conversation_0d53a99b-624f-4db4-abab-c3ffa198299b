/**
 * 设置应用主入口
 * 专门用于独立的设置页面
 */

// 导入核心模块
import { Store } from './state/store.js';
import { SettingsComponent } from './components/settings.js';
import { ToastComponent } from './components/toast.js';
import { EventManager } from './utils/events.js';
import { StorageUtils } from './utils/storage.js';
import { globalThemeManager } from './utils/theme.js';

/**
 * 设置应用类
 */
class SettingsApp {
  constructor() {
    this.store = null;
    this.eventManager = null;
    this.components = new Map();
    this.isInitialized = false;
    
    // 绑定方法上下文
    this.init = this.init.bind(this);
    this.destroy = this.destroy.bind(this);
  }

  /**
   * 初始化应用
   */
  async init() {
    if (this.isInitialized) {
      console.warn('Settings app already initialized');
      return;
    }

    try {
      console.log('Initializing Settings App...');
      
      // 初始化核心系统
      await this.initializeCore();
      
      // 初始化组件
      await this.initializeComponents();
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 加载初始数据
      this.loadInitialData();
      
      this.isInitialized = true;
      console.log('Settings App initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize Settings App:', error);
      throw error;
    }
  }

  /**
   * 初始化核心系统
   */
  async initializeCore() {
    // 初始化Store
    this.store = new Store();
    
    // 初始化事件管理器
    this.eventManager = new EventManager();
    
    // 初始化主题管理器
    globalThemeManager.init();
    
    console.log('Core systems initialized');
  }

  /**
   * 初始化组件
   */
  async initializeComponents() {
    // 初始化Toast组件
    const toastComponent = new ToastComponent();
    this.components.set('toast', toastComponent);
    
    // 初始化设置组件
    const settingsComponent = new SettingsComponent(this.store, this.eventManager);
    this.components.set('settings', settingsComponent);
    
    // 初始化所有组件
    for (const [componentName, component] of this.components) {
      try {
        await component.init();
        console.log(`Component ${componentName} initialized`);
      } catch (error) {
        console.error(`Failed to initialize component ${componentName}:`, error);
      }
    }
    
    // 显示设置页面
    const settingsComp = this.components.get('settings');
    if (settingsComp) {
      settingsComp.show();
    }
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 监听主题变化
    document.addEventListener('themechange', (e) => {
      console.log('Theme changed:', e.detail);
    });
    
    // 监听设置变化
    this.eventManager.on('settingChanged', (data) => {
      console.log('Setting changed:', data);
    });
    
    // 监听Toast事件
    this.eventManager.on('toast:show', (data) => {
      const toastComponent = this.components.get('toast');
      if (toastComponent) {
        toastComponent.show(data.message, data.type, data.options);
      }
    });
    
    // 监听窗口事件
    window.addEventListener('beforeunload', () => {
      this.destroy();
    });
    
    // 监听键盘事件
    document.addEventListener('keydown', (e) => {
      // Esc键返回聊天页面
      if (e.key === 'Escape') {
        this.navigateToChat();
      }
    });
  }

  /**
   * 加载初始数据
   */
  loadInitialData() {
    // 加载设置
    const settings = StorageUtils.getSettings();
    this.store.setState('settings', settings);
    
    // 应用主题
    if (settings.theme) {
      globalThemeManager.setTheme(settings.theme);
    }
    
    // 应用字号
    if (settings.fontSize) {
      document.documentElement.setAttribute('data-font-size', settings.fontSize);
    }
    
    console.log('Initial data loaded');
  }

  /**
   * 导航到聊天页面
   */
  navigateToChat() {
    // 在VS Code扩展环境中，这里可以发送消息给扩展
    if (window.parent && window.parent.postMessage) {
      window.parent.postMessage({
        type: 'navigateToChat'
      }, '*');
    } else {
      // 在测试环境中，可以直接跳转
      window.location.href = './chat.html';
    }
  }

  /**
   * 获取组件
   * @param {string} name - 组件名称
   * @returns {object|null} 组件实例
   */
  getComponent(name) {
    return this.components.get(name) || null;
  }

  /**
   * 获取Store
   * @returns {Store} Store实例
   */
  getStore() {
    return this.store;
  }

  /**
   * 获取事件管理器
   * @returns {EventManager} 事件管理器实例
   */
  getEventManager() {
    return this.eventManager;
  }

  /**
   * 销毁应用
   */
  destroy() {
    if (!this.isInitialized) {
      return;
    }

    try {
      console.log('Destroying Settings App...');
      
      // 销毁组件
      for (const [, component] of this.components) {
        if (component.destroy) {
          component.destroy();
        }
      }
      
      // 清理事件监听
      this.eventManager.removeAllListeners();
      
      this.isInitialized = false;
      console.log('Settings App destroyed');
      
    } catch (error) {
      console.error('Error destroying Settings App:', error);
    }
  }
}

// 创建并初始化应用实例
const settingsApp = new SettingsApp();

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    settingsApp.init().catch(console.error);
  });
} else {
  settingsApp.init().catch(console.error);
}

// 导出应用实例
export default settingsApp;

// 将应用实例挂载到全局对象，方便调试
window.settingsApp = settingsApp;
