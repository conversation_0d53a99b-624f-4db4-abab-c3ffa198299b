<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息版本载入修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .message-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .version-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>消息版本载入修复测试</h1>
        <p>测试修复后的消息版本载入功能，确保历史记录中的多版本消息显示正确的版本内容。</p>

        <div class="test-section">
            <div class="test-title">测试1: 模拟历史记录载入</div>
            <button onclick="testHistoryLoading()">运行测试</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试2: 版本切换功能</div>
            <button onclick="testVersionSwitching()">运行测试</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试3: 边界情况处理</div>
            <button onclick="testEdgeCases()">运行测试</button>
            <div id="test3-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试4: 编辑模式和导出功能</div>
            <button onclick="testEditAndExport()">运行测试</button>
            <div id="test4-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">消息预览</div>
            <div id="message-preview"></div>
        </div>
    </div>

    <script type="module">
        // 模拟必要的依赖
        window.DOMUtils = {
            createElement: (tag, props = {}) => {
                const element = document.createElement(tag);
                Object.entries(props).forEach(([key, value]) => {
                    if (key === 'className') {
                        element.className = value;
                    } else if (key === 'textContent') {
                        element.textContent = value;
                    } else if (key === 'innerHTML') {
                        element.innerHTML = value;
                    } else {
                        element.setAttribute(key, value);
                    }
                });
                return element;
            }
        };

        // 模拟消息组件的核心方法
        class MockMessageComponent {
            getCurrentVersionContent(message) {
                // 如果消息有版本系统
                if (message.versions && message.versions.length > 0) {
                    const currentIndex = message.currentVersionIndex || 0;
                    // 确保索引在有效范围内
                    if (currentIndex >= 0 && currentIndex < message.versions.length) {
                        const currentVersion = message.versions[currentIndex];
                        return currentVersion ? currentVersion.content : message.content;
                    }
                }
                // 向后兼容：如果没有版本系统或索引无效，使用原始内容
                return message.content;
            }

            createMessageElement(message) {
                const messageDiv = DOMUtils.createElement('div', {
                    className: `message ${message.isUser ? 'message-user' : 'message-ai'}`,
                    'data-message-id': message.id
                });
                
                const messageContent = DOMUtils.createElement('div', {
                    className: 'message-content'
                });
                
                // 消息文本 - 使用当前版本的内容
                const messageText = DOMUtils.createElement('div', {
                    className: 'message-text',
                    textContent: this.getCurrentVersionContent(message)
                });
                
                messageContent.appendChild(messageText);
                messageDiv.appendChild(messageContent);
                
                return messageDiv;
            }
        }

        const mockComponent = new MockMessageComponent();

        // 测试函数
        window.testHistoryLoading = function() {
            const resultDiv = document.getElementById('test1-result');
            resultDiv.innerHTML = '';

            try {
                // 模拟一个有多个版本的历史消息
                const historyMessage = {
                    id: 'msg-001',
                    content: '原始内容', // 这可能是过时的内容
                    isUser: true,
                    timestamp: Date.now() - 10000,
                    versions: [
                        { content: '原始内容', timestamp: Date.now() - 10000, aiReply: null },
                        { content: '第一次编辑', timestamp: Date.now() - 5000, aiReply: null },
                        { content: '第二次编辑', timestamp: Date.now() - 1000, aiReply: null }
                    ],
                    currentVersionIndex: 2 // 当前应该显示第三个版本
                };

                // 测试修复前的问题（直接使用 message.content）
                const oldContent = historyMessage.content;
                
                // 测试修复后的正确行为
                const correctContent = mockComponent.getCurrentVersionContent(historyMessage);
                
                // 创建消息元素
                const messageElement = mockComponent.createMessageElement(historyMessage);
                const displayedContent = messageElement.querySelector('.message-text').textContent;

                // 验证结果
                if (displayedContent === '第二次编辑') {
                    resultDiv.innerHTML += '<div class="test-result success">✓ 测试通过：正确显示当前版本内容</div>';
                    resultDiv.innerHTML += `<div class="test-result info">显示内容: "${displayedContent}"</div>`;
                } else {
                    resultDiv.innerHTML += '<div class="test-result error">✗ 测试失败：显示了错误的版本内容</div>';
                    resultDiv.innerHTML += `<div class="test-result error">期望: "第二次编辑", 实际: "${displayedContent}"</div>`;
                }

                // 显示消息预览
                const previewDiv = document.getElementById('message-preview');
                previewDiv.innerHTML = '';
                previewDiv.appendChild(messageElement);
                previewDiv.innerHTML += `<div class="version-info">当前版本索引: ${historyMessage.currentVersionIndex}, 总版本数: ${historyMessage.versions.length}</div>`;

            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">测试出错: ${error.message}</div>`;
            }
        };

        window.testVersionSwitching = function() {
            const resultDiv = document.getElementById('test2-result');
            resultDiv.innerHTML = '';

            try {
                const message = {
                    id: 'msg-002',
                    content: '当前内容',
                    isUser: true,
                    versions: [
                        { content: '版本1', timestamp: Date.now() - 3000 },
                        { content: '版本2', timestamp: Date.now() - 2000 },
                        { content: '版本3', timestamp: Date.now() - 1000 }
                    ],
                    currentVersionIndex: 1
                };

                // 测试不同版本索引
                const tests = [
                    { index: 0, expected: '版本1' },
                    { index: 1, expected: '版本2' },
                    { index: 2, expected: '版本3' }
                ];

                let allPassed = true;
                tests.forEach(test => {
                    message.currentVersionIndex = test.index;
                    const content = mockComponent.getCurrentVersionContent(message);
                    if (content === test.expected) {
                        resultDiv.innerHTML += `<div class="test-result success">✓ 版本${test.index + 1}测试通过</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="test-result error">✗ 版本${test.index + 1}测试失败: 期望"${test.expected}", 实际"${content}"</div>`;
                        allPassed = false;
                    }
                });

                if (allPassed) {
                    resultDiv.innerHTML += '<div class="test-result success">✓ 所有版本切换测试通过</div>';
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">测试出错: ${error.message}</div>`;
            }
        };

        window.testEdgeCases = function() {
            const resultDiv = document.getElementById('test3-result');
            resultDiv.innerHTML = '';

            try {
                const testCases = [
                    {
                        name: '无版本系统的旧消息',
                        message: { id: 'msg-old', content: '旧消息内容', isUser: true },
                        expected: '旧消息内容'
                    },
                    {
                        name: '空版本数组',
                        message: { id: 'msg-empty', content: '备用内容', versions: [], currentVersionIndex: 0 },
                        expected: '备用内容'
                    },
                    {
                        name: '无效版本索引',
                        message: { 
                            id: 'msg-invalid', 
                            content: '备用内容', 
                            versions: [{ content: '版本1' }], 
                            currentVersionIndex: 5 
                        },
                        expected: '备用内容'
                    },
                    {
                        name: '负数版本索引',
                        message: { 
                            id: 'msg-negative', 
                            content: '备用内容', 
                            versions: [{ content: '版本1' }], 
                            currentVersionIndex: -1 
                        },
                        expected: '备用内容'
                    }
                ];

                let allPassed = true;
                testCases.forEach(testCase => {
                    const content = mockComponent.getCurrentVersionContent(testCase.message);
                    if (content === testCase.expected) {
                        resultDiv.innerHTML += `<div class="test-result success">✓ ${testCase.name}: 通过</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="test-result error">✗ ${testCase.name}: 失败 (期望"${testCase.expected}", 实际"${content}")</div>`;
                        allPassed = false;
                    }
                });

                if (allPassed) {
                    resultDiv.innerHTML += '<div class="test-result success">✓ 所有边界情况测试通过</div>';
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">测试出错: ${error.message}</div>`;
            }
        };

        window.testEditAndExport = function() {
            const resultDiv = document.getElementById('test4-result');
            resultDiv.innerHTML = '';

            try {
                // 测试编辑模式中的内容获取
                const message = {
                    id: 'msg-edit-test',
                    content: '过时的内容', // 模拟过时的 message.content
                    isUser: true,
                    versions: [
                        { content: '原始版本', timestamp: Date.now() - 3000 },
                        { content: '编辑版本1', timestamp: Date.now() - 2000 },
                        { content: '最新编辑版本', timestamp: Date.now() - 1000 }
                    ],
                    currentVersionIndex: 2 // 应该显示最新编辑版本
                };

                // 测试获取当前版本内容
                const currentContent = mockComponent.getCurrentVersionContent(message);

                if (currentContent === '最新编辑版本') {
                    resultDiv.innerHTML += '<div class="test-result success">✓ 编辑模式内容获取正确</div>';
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">✗ 编辑模式内容获取错误: 期望"最新编辑版本", 实际"${currentContent}"</div>`;
                }

                // 测试消息元素创建
                const messageElement = mockComponent.createMessageElement(message);
                const displayedContent = messageElement.querySelector('.message-text').textContent;

                if (displayedContent === '最新编辑版本') {
                    resultDiv.innerHTML += '<div class="test-result success">✓ 消息元素显示内容正确</div>';
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">✗ 消息元素显示内容错误: 期望"最新编辑版本", 实际"${displayedContent}"</div>`;
                }

                // 测试不同版本索引的情况
                const testVersions = [0, 1, 2];
                const expectedContents = ['原始版本', '编辑版本1', '最新编辑版本'];

                let versionTestsPassed = true;
                testVersions.forEach((versionIndex, i) => {
                    message.currentVersionIndex = versionIndex;
                    const content = mockComponent.getCurrentVersionContent(message);
                    if (content !== expectedContents[i]) {
                        versionTestsPassed = false;
                        resultDiv.innerHTML += `<div class="test-result error">✗ 版本${versionIndex}测试失败: 期望"${expectedContents[i]}", 实际"${content}"</div>`;
                    }
                });

                if (versionTestsPassed) {
                    resultDiv.innerHTML += '<div class="test-result success">✓ 所有版本索引测试通过</div>';
                }

                resultDiv.innerHTML += '<div class="test-result info">编辑和导出功能测试完成</div>';

            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">测试出错: ${error.message}</div>`;
            }
        };

        // 页面加载完成后自动运行第一个测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('消息版本载入修复测试页面已加载');
        });
    </script>
</body>
</html>
