/**
 * 主题管理工具
 * 提供主题切换、检测和管理功能
 */

/**
 * 主题管理类
 */
export class ThemeManager {
  constructor() {
    this.themes = ['auto', 'light', 'dark', 'high-contrast'];
    this.currentTheme = 'auto';
    this.systemTheme = this.detectSystemTheme();
    this.vscodeTheme = this.detectVSCodeTheme();
    this.observers = [];

    // 监听系统主题变化
    this.setupSystemThemeListener();

    // 监听VS Code主题变化
    this.setupVSCodeThemeListener();

    // 绑定方法上下文
    this.setTheme = this.setTheme.bind(this);
    this.getTheme = this.getTheme.bind(this);
    this.toggleTheme = this.toggleTheme.bind(this);
  }

  /**
   * 检测系统主题
   * @returns {string} 系统主题
   */
  detectSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }

  /**
   * 检测VS Code主题
   * @returns {object} VS Code主题信息
   */
  detectVSCodeTheme() {
    const body = document.body;
    const themeKind = body.getAttribute('data-vscode-theme-kind');
    const themeName = body.getAttribute('data-vscode-theme-name');

    return {
      kind: themeKind,
      name: themeName,
      isDark: themeKind && themeKind.includes('dark'),
      isLight: themeKind && themeKind.includes('light'),
      isHighContrast: themeKind && themeKind.includes('high-contrast')
    };
  }

  /**
   * 设置主题监听器
   */
  setupSystemThemeListener() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', (e) => {
        this.systemTheme = e.matches ? 'dark' : 'light';
        if (this.currentTheme === 'auto') {
          this.applyTheme('auto');
        }
      });
    }
  }

  /**
   * 设置VS Code主题监听器
   */
  setupVSCodeThemeListener() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' &&
            (mutation.attributeName === 'data-vscode-theme-kind' ||
             mutation.attributeName === 'data-vscode-theme-name')) {
          this.vscodeTheme = this.detectVSCodeTheme();
          this.notifyObservers();
          if (this.currentTheme === 'auto') {
            this.applyTheme('auto');
          }
        }
      });
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['data-vscode-theme-kind', 'data-vscode-theme-name']
    });
  }

  /**
   * 设置主题
   * @param {string} theme - 主题名称
   * @returns {boolean} 是否成功
   */
  setTheme(theme) {
    if (!this.themes.includes(theme)) {
      console.warn(`Invalid theme: ${theme}`);
      return false;
    }

    this.currentTheme = theme;
    this.applyTheme(theme);
    this.saveTheme(theme);
    
    // 触发主题变化事件
    this.dispatchThemeChangeEvent(theme);
    
    return true;
  }

  /**
   * 应用主题
   * @param {string} theme - 主题名称
   */
  applyTheme(theme) {
    const root = document.documentElement;
    
    // 添加主题切换类（防止动画闪烁）
    root.classList.add('theme-switching');
    
    // 设置主题属性
    if (theme === 'auto') {
      root.setAttribute('data-theme', this.systemTheme);
    } else {
      root.setAttribute('data-theme', theme);
    }
    
    // 移除主题切换类
    setTimeout(() => {
      root.classList.remove('theme-switching');
    }, 100);
  }

  /**
   * 获取当前主题
   * @returns {string} 当前主题
   */
  getTheme() {
    return this.currentTheme;
  }

  /**
   * 获取VS Code主题信息
   * @returns {object} VS Code主题信息
   */
  getVSCodeTheme() {
    return this.vscodeTheme;
  }

  /**
   * 检查主题兼容性
   * @returns {object} 兼容性信息
   */
  checkThemeCompatibility() {
    const compatibility = {
      vsCodeIntegration: !!this.vscodeTheme.kind,
      systemThemeSupport: !!window.matchMedia,
      highContrastSupport: window.matchMedia('(prefers-contrast: high)').matches,
      reducedMotionSupport: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      forcedColorsSupport: window.matchMedia('(forced-colors: active)').matches
    };

    return {
      ...compatibility,
      overallScore: Object.values(compatibility).filter(Boolean).length / Object.keys(compatibility).length
    };
  }

  /**
   * 添加主题变化观察者
   * @param {Function} callback - 回调函数
   */
  addObserver(callback) {
    this.observers.push(callback);
  }

  /**
   * 移除主题变化观察者
   * @param {Function} callback - 回调函数
   */
  removeObserver(callback) {
    const index = this.observers.indexOf(callback);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  /**
   * 通知观察者
   */
  notifyObservers() {
    const themeInfo = {
      current: this.currentTheme,
      system: this.systemTheme,
      vscode: this.vscodeTheme,
      compatibility: this.checkThemeCompatibility()
    };

    this.observers.forEach(callback => {
      try {
        callback(themeInfo);
      } catch (error) {
        console.error('Theme observer error:', error);
      }
    });
  }

  /**
   * 获取实际应用的主题
   * @returns {string} 实际主题
   */
  getAppliedTheme() {
    if (this.currentTheme === 'auto') {
      return this.systemTheme;
    }
    return this.currentTheme;
  }

  /**
   * 切换主题
   * @returns {string} 新主题
   */
  toggleTheme() {
    const currentIndex = this.themes.indexOf(this.currentTheme);
    const nextIndex = (currentIndex + 1) % this.themes.length;
    const nextTheme = this.themes[nextIndex];
    
    this.setTheme(nextTheme);
    return nextTheme;
  }

  /**
   * 保存主题到本地存储
   * @param {string} theme - 主题名称
   */
  saveTheme(theme) {
    try {
      localStorage.setItem('chat-theme', theme);
    } catch (error) {
      console.error('Failed to save theme:', error);
    }
  }

  /**
   * 从本地存储加载主题
   * @returns {string} 加载的主题
   */
  loadTheme() {
    try {
      const savedTheme = localStorage.getItem('chat-theme');
      if (savedTheme && this.themes.includes(savedTheme)) {
        return savedTheme;
      }
    } catch (error) {
      console.error('Failed to load theme:', error);
    }
    return 'auto';
  }

  /**
   * 初始化主题
   */
  init() {
    const savedTheme = this.loadTheme();
    this.setTheme(savedTheme);
  }

  /**
   * 触发主题变化事件
   * @param {string} theme - 新主题
   */
  dispatchThemeChangeEvent(theme) {
    const event = new CustomEvent('themechange', {
      detail: {
        theme,
        appliedTheme: this.getAppliedTheme(),
        systemTheme: this.systemTheme
      }
    });
    
    document.dispatchEvent(event);
  }

  /**
   * 获取主题信息
   * @returns {object} 主题信息
   */
  getThemeInfo() {
    return {
      current: this.currentTheme,
      applied: this.getAppliedTheme(),
      system: this.systemTheme,
      available: [...this.themes]
    };
  }

  /**
   * 检查是否为暗色主题
   * @returns {boolean} 是否为暗色主题
   */
  isDarkTheme() {
    const appliedTheme = this.getAppliedTheme();
    return appliedTheme === 'dark' || appliedTheme === 'high-contrast';
  }

  /**
   * 检查是否为亮色主题
   * @returns {boolean} 是否为亮色主题
   */
  isLightTheme() {
    const appliedTheme = this.getAppliedTheme();
    return appliedTheme === 'light';
  }

  /**
   * 检查是否为高对比度主题
   * @returns {boolean} 是否为高对比度主题
   */
  isHighContrastTheme() {
    return this.getAppliedTheme() === 'high-contrast';
  }

  /**
   * 获取主题颜色
   * @param {string} colorName - 颜色名称
   * @returns {string} 颜色值
   */
  getThemeColor(colorName) {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    return computedStyle.getPropertyValue(`--color-${colorName}`).trim();
  }

  /**
   * 设置自定义主题颜色
   * @param {object} colors - 颜色对象
   */
  setCustomColors(colors) {
    const root = document.documentElement;
    
    Object.entries(colors).forEach(([name, value]) => {
      root.style.setProperty(`--color-${name}`, value);
    });
  }

  /**
   * 重置自定义颜色
   */
  resetCustomColors() {
    const root = document.documentElement;
    const customProperties = Array.from(root.style).filter(prop => 
      prop.startsWith('--color-')
    );
    
    customProperties.forEach(prop => {
      root.style.removeProperty(prop);
    });
  }

  /**
   * 销毁主题管理器
   */
  destroy() {
    // 移除事件监听器
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.removeEventListener('change', this.handleSystemThemeChange);
    }
  }
}

/**
 * 主题工具函数
 */
export const ThemeUtils = {
  /**
   * 获取CSS变量值
   * @param {string} varName - 变量名
   * @returns {string} 变量值
   */
  getCSSVariable(varName) {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(varName)
      .trim();
  },

  /**
   * 设置CSS变量值
   * @param {string} varName - 变量名
   * @param {string} value - 变量值
   */
  setCSSVariable(varName, value) {
    document.documentElement.style.setProperty(varName, value);
  },

  /**
   * 检查是否支持CSS变量
   * @returns {boolean} 是否支持
   */
  supportsCSSVariables() {
    return window.CSS && CSS.supports('color', 'var(--test)');
  },

  /**
   * 检查是否支持暗色模式媒体查询
   * @returns {boolean} 是否支持
   */
  supportsDarkModeQuery() {
    return window.matchMedia && 
           window.matchMedia('(prefers-color-scheme: dark)').media !== 'not all';
  }
};

// 创建全局主题管理器实例
export const globalThemeManager = new ThemeManager();

// 导出默认实例
export default globalThemeManager;
