<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息渲染系统测试 - </title>
    <link rel="stylesheet" href="../styles/variables.css">
    <link rel="stylesheet" href="../styles/components.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a9e;
        }
        .messages-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
            background: #fafafa;
            margin-top: 15px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>消息渲染系统测试 - </h1>
        <p>测试新的消息渲染系统，支持内嵌AI回复显示</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 基础消息渲染测试</h3>
            <div class="controls">
                <button class="button" onclick="testBasicUserMessage()">测试用户消息</button>
                <button class="button" onclick="testMessageWithAIReply()">测试带AI回复的消息</button>
                <button class="button" onclick="testMultipleVersions()">测试多版本消息</button>
                <button class="button" onclick="clearMessages()">清空消息</button>
            </div>
            <div id="basicMessagesContainer" class="messages-container"></div>
            <div id="basicResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. AI回复状态测试</h3>
            <div class="controls">
                <button class="button" onclick="testPendingStatus()">等待状态</button>
                <button class="button" onclick="testGeneratingStatus()">生成中状态</button>
                <button class="button" onclick="testCompletedStatus()">完成状态</button>
                <button class="button" onclick="testErrorStatus()">错误状态</button>
            </div>
            <div id="statusMessagesContainer" class="messages-container"></div>
            <div id="statusResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 动态更新测试</h3>
            <div class="controls">
                <button class="button" onclick="testStatusUpdate()">测试状态更新</button>
                <button class="button" onclick="testContentUpdate()">测试内容更新</button>
                <button class="button" onclick="testAddAIReply()">添加AI回复</button>
                <button class="button" onclick="testRemoveAIReply()">移除AI回复</button>
            </div>
            <div id="updateMessagesContainer" class="messages-container"></div>
            <div id="updateResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 交互功能测试</h3>
            <div class="controls">
                <button class="button" onclick="testMessageActions()">测试消息操作</button>
                <button class="button" onclick="testAIReplyActions()">测试AI回复操作</button>
                <button class="button" onclick="testVersionSwitching()">测试版本切换</button>
            </div>
            <div id="interactionMessagesContainer" class="messages-container"></div>
            <div id="interactionResult" class="result"></div>
        </div>
    </div>

    <script type="module">
        // 模拟消息组件的核心渲染功能
        class MessageRenderer {
            constructor(container) {
                this.container = container;
                this.messages = [];
            }

            // 创建用户消息内容
            createUserMessageContent(message, version) {
                const userContent = document.createElement('div');
                userContent.className = 'user-message-content';

                const messageText = document.createElement('div');
                messageText.className = 'message-text user-message-text';
                messageText.textContent = version.content;

                const messageActions = document.createElement('div');
                messageActions.className = 'message-actions';
                messageActions.innerHTML = `
                    <button class="btn btn-icon" title="编辑">✏️</button>
                    <button class="btn btn-icon" title="复制">📋</button>
                    <button class="btn btn-icon" title="删除">🗑️</button>
                `;

                userContent.appendChild(messageText);
                userContent.appendChild(messageActions);

                return userContent;
            }

            // 创建AI回复状态指示器
            createAIReplyStatusIndicator(aiReply) {
                const statusIndicator = document.createElement('div');
                statusIndicator.className = `ai-reply-status ai-reply-status-${aiReply.status}`;

                let statusContent = '';
                let statusClass = '';

                switch (aiReply.status) {
                    case 'pending':
                        statusContent = '⏳ 等待回复...';
                        statusClass = 'status-pending';
                        break;
                    case 'generating':
                        statusContent = `🤖 正在生成... ${aiReply.progress || 0}%`;
                        statusClass = 'status-generating';
                        break;
                    case 'completed':
                        statusContent = '✅ AI回复';
                        statusClass = 'status-completed';
                        break;
                    case 'error':
                        statusContent = '❌ 生成失败';
                        statusClass = 'status-error';
                        break;
                    case 'cancelled':
                        statusContent = '⏹️ 已取消';
                        statusClass = 'status-cancelled';
                        break;
                    case 'timeout':
                        statusContent = '⏰ 超时';
                        statusClass = 'status-timeout';
                        break;
                    default:
                        statusContent = '🤖 AI回复';
                        statusClass = 'status-unknown';
                }

                statusIndicator.className += ` ${statusClass}`;
                statusIndicator.textContent = statusContent;

                return statusIndicator;
            }

            // 创建AI回复内容
            createAIReplyContent(aiReply, message) {
                const aiContent = document.createElement('div');
                aiContent.className = 'ai-reply-content';
                aiContent.setAttribute('data-ai-reply-id', aiReply.id);

                const statusIndicator = this.createAIReplyStatusIndicator(aiReply);
                aiContent.appendChild(statusIndicator);

                const aiText = document.createElement('div');
                aiText.className = 'message-text ai-reply-text';
                aiText.textContent = aiReply.content;
                aiContent.appendChild(aiText);

                const aiActions = document.createElement('div');
                aiActions.className = 'ai-reply-actions';
                aiActions.innerHTML = `
                    <button class="btn btn-icon" title="重新生成">🔄</button>
                    <button class="btn btn-icon" title="复制">📋</button>
                `;
                aiContent.appendChild(aiActions);

                if (aiReply.metadata && (aiReply.metadata.model || aiReply.metadata.tokens)) {
                    const metadata = document.createElement('div');
                    metadata.className = 'ai-reply-metadata';
                    const metadataItems = [];
                    if (aiReply.metadata.model) metadataItems.push(`模型: ${aiReply.metadata.model}`);
                    if (aiReply.metadata.tokens) metadataItems.push(`Token: ${aiReply.metadata.tokens}`);
                    metadata.textContent = metadataItems.join(' • ');
                    aiContent.appendChild(metadata);
                }

                return aiContent;
            }

            // 创建消息元素
            createMessageElement(message) {
                if (!message.isUser) {
                    console.warn('不应该渲染独立的AI消息');
                    return null;
                }

                const messageDiv = document.createElement('div');
                messageDiv.className = 'message message-user';
                messageDiv.setAttribute('data-message-id', message.id);

                const currentVersion = message.versions[message.currentVersionIndex || 0];
                if (!currentVersion) {
                    console.error('消息没有有效的当前版本');
                    return null;
                }

                const userMessageContent = this.createUserMessageContent(message, currentVersion);
                messageDiv.appendChild(userMessageContent);

                if (currentVersion.aiReply) {
                    const aiReplyContent = this.createAIReplyContent(currentVersion.aiReply, message);
                    messageDiv.appendChild(aiReplyContent);
                }

                const messageTime = document.createElement('div');
                messageTime.className = 'message-time';
                messageTime.textContent = new Date(message.timestamp).toLocaleTimeString();
                messageDiv.appendChild(messageTime);

                return messageDiv;
            }

            // 渲染消息
            render() {
                this.container.innerHTML = '';
                this.messages.forEach(message => {
                    const element = this.createMessageElement(message);
                    if (element) {
                        this.container.appendChild(element);
                    }
                });
            }

            // 添加消息
            addMessage(messageData) {
                this.messages.push(messageData);
                this.render();
            }

            // 清空消息
            clear() {
                this.messages = [];
                this.render();
            }

            // 更新AI回复状态
            updateAIReplyStatus(messageId, aiReply) {
                const message = this.messages.find(m => m.id === messageId);
                if (message && message.versions[message.currentVersionIndex]) {
                    message.versions[message.currentVersionIndex].aiReply = aiReply;
                    this.render();
                }
            }
        }

        // 全局渲染器实例
        const basicRenderer = new MessageRenderer(document.getElementById('basicMessagesContainer'));
        const statusRenderer = new MessageRenderer(document.getElementById('statusMessagesContainer'));
        const updateRenderer = new MessageRenderer(document.getElementById('updateMessagesContainer'));
        const interactionRenderer = new MessageRenderer(document.getElementById('interactionMessagesContainer'));

        // 导出测试函数到全局作用域
        window.testBasicUserMessage = testBasicUserMessage;
        window.testMessageWithAIReply = testMessageWithAIReply;
        window.testMultipleVersions = testMultipleVersions;
        window.clearMessages = clearMessages;
        window.testPendingStatus = testPendingStatus;
        window.testGeneratingStatus = testGeneratingStatus;
        window.testCompletedStatus = testCompletedStatus;
        window.testErrorStatus = testErrorStatus;
        window.testStatusUpdate = testStatusUpdate;
        window.testContentUpdate = testContentUpdate;
        window.testAddAIReply = testAddAIReply;
        window.testRemoveAIReply = testRemoveAIReply;
        window.testMessageActions = testMessageActions;
        window.testAIReplyActions = testAIReplyActions;
        window.testVersionSwitching = testVersionSwitching;

        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        function createSampleMessage(content, hasAIReply = false, aiReplyData = {}) {
            const message = {
                id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                isUser: true,
                timestamp: Date.now(),
                edited: false,
                collapsed: false,
                type: 'text',
                versions: [{
                    id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    content: content,
                    timestamp: Date.now(),
                    aiReply: hasAIReply ? {
                        id: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                        content: aiReplyData.content || 'AI回复内容',
                        timestamp: Date.now(),
                        status: aiReplyData.status || 'completed',
                        progress: aiReplyData.progress || 100,
                        metadata: aiReplyData.metadata || {}
                    } : null,
                    metadata: {}
                }],
                currentVersionIndex: 0,
                metadata: {}
            };
            return message;
        }

        // 1. 基础消息渲染测试
        function testBasicUserMessage() {
            const message = createSampleMessage('这是一条测试用户消息');
            basicRenderer.addMessage(message);
            showResult('basicResult', '✅ 基础用户消息渲染成功', 'success');
        }

        function testMessageWithAIReply() {
            const message = createSampleMessage('用户问题：什么是JavaScript？', true, {
                content: 'JavaScript是一种高级的、解释型的编程语言，主要用于网页开发。',
                status: 'completed',
                metadata: { model: 'GPT-4', tokens: 150 }
            });
            basicRenderer.addMessage(message);
            showResult('basicResult', '✅ 带AI回复的消息渲染成功', 'success');
        }

        function testMultipleVersions() {
            const message = createSampleMessage('原始消息内容');
            message.versions.push({
                id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                content: '编辑后的消息内容',
                timestamp: Date.now(),
                aiReply: {
                    id: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    content: '针对编辑后内容的AI回复',
                    timestamp: Date.now(),
                    status: 'completed',
                    progress: 100,
                    metadata: {}
                },
                metadata: {}
            });
            message.currentVersionIndex = 1;
            basicRenderer.addMessage(message);
            showResult('basicResult', '✅ 多版本消息渲染成功', 'success');
        }

        function clearMessages() {
            basicRenderer.clear();
            showResult('basicResult', '✅ 消息已清空', 'info');
        }

        // 2. AI回复状态测试
        function testPendingStatus() {
            const message = createSampleMessage('等待AI回复的消息', true, {
                content: '',
                status: 'pending',
                progress: 0
            });
            statusRenderer.addMessage(message);
            showResult('statusResult', '✅ 等待状态显示成功', 'success');
        }

        function testGeneratingStatus() {
            const message = createSampleMessage('AI正在生成回复', true, {
                content: '正在生成中...',
                status: 'generating',
                progress: 45
            });
            statusRenderer.addMessage(message);
            showResult('statusResult', '✅ 生成中状态显示成功', 'success');
        }

        function testCompletedStatus() {
            const message = createSampleMessage('AI已完成回复', true, {
                content: '这是完整的AI回复内容。',
                status: 'completed',
                progress: 100,
                metadata: { model: 'GPT-4', tokens: 200, duration: 2500 }
            });
            statusRenderer.addMessage(message);
            showResult('statusResult', '✅ 完成状态显示成功', 'success');
        }

        function testErrorStatus() {
            const message = createSampleMessage('AI回复失败的消息', true, {
                content: '',
                status: 'error',
                progress: 0,
                metadata: { error: '网络连接失败' }
            });
            statusRenderer.addMessage(message);
            showResult('statusResult', '✅ 错误状态显示成功', 'success');
        }

        // 3. 动态更新测试
        let updateTestMessage = null;

        function testStatusUpdate() {
            updateTestMessage = createSampleMessage('测试状态更新', true, {
                content: '',
                status: 'pending',
                progress: 0
            });
            updateRenderer.addMessage(updateTestMessage);

            // 模拟状态更新过程
            setTimeout(() => {
                updateRenderer.updateAIReplyStatus(updateTestMessage.id, {
                    ...updateTestMessage.versions[0].aiReply,
                    status: 'generating',
                    progress: 30,
                    content: '开始生成...'
                });
            }, 1000);

            setTimeout(() => {
                updateRenderer.updateAIReplyStatus(updateTestMessage.id, {
                    ...updateTestMessage.versions[0].aiReply,
                    status: 'generating',
                    progress: 70,
                    content: '继续生成中...'
                });
            }, 2000);

            setTimeout(() => {
                updateRenderer.updateAIReplyStatus(updateTestMessage.id, {
                    ...updateTestMessage.versions[0].aiReply,
                    status: 'completed',
                    progress: 100,
                    content: '完整的AI回复内容已生成完成。'
                });
            }, 3000);

            showResult('updateResult', '✅ 状态更新测试开始，请观察变化', 'info');
        }

        function testContentUpdate() {
            if (!updateTestMessage) {
                updateTestMessage = createSampleMessage('测试内容更新', true, {
                    content: '原始AI回复内容',
                    status: 'completed'
                });
                updateRenderer.addMessage(updateTestMessage);
            }

            updateRenderer.updateAIReplyStatus(updateTestMessage.id, {
                ...updateTestMessage.versions[0].aiReply,
                content: '更新后的AI回复内容 - ' + new Date().toLocaleTimeString()
            });

            showResult('updateResult', '✅ 内容更新成功', 'success');
        }

        function testAddAIReply() {
            const message = createSampleMessage('没有AI回复的消息');
            updateRenderer.addMessage(message);

            setTimeout(() => {
                updateRenderer.updateAIReplyStatus(message.id, {
                    id: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    content: '新添加的AI回复',
                    timestamp: Date.now(),
                    status: 'completed',
                    progress: 100,
                    metadata: {}
                });
            }, 500);

            showResult('updateResult', '✅ AI回复添加测试开始', 'info');
        }

        function testRemoveAIReply() {
            if (!updateTestMessage) {
                testAddAIReply();
                return;
            }

            updateRenderer.updateAIReplyStatus(updateTestMessage.id, null);
            showResult('updateResult', '✅ AI回复移除成功', 'success');
        }

        // 4. 交互功能测试
        function testMessageActions() {
            const message = createSampleMessage('测试消息操作功能');
            interactionRenderer.addMessage(message);
            showResult('interactionResult', '✅ 消息操作按钮已显示，请悬停查看', 'info');
        }

        function testAIReplyActions() {
            const message = createSampleMessage('测试AI回复操作', true, {
                content: 'AI回复内容，请悬停查看操作按钮',
                status: 'completed'
            });
            interactionRenderer.addMessage(message);
            showResult('interactionResult', '✅ AI回复操作按钮已显示，请悬停查看', 'info');
        }

        function testVersionSwitching() {
            const message = createSampleMessage('版本1的内容');
            message.versions.push({
                id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                content: '版本2的内容',
                timestamp: Date.now(),
                aiReply: {
                    id: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    content: '版本2对应的AI回复',
                    timestamp: Date.now(),
                    status: 'completed',
                    progress: 100,
                    metadata: {}
                },
                metadata: {}
            });
            interactionRenderer.addMessage(message);
            showResult('interactionResult', '✅ 多版本消息已创建，版本切换功能需要完整组件支持', 'info');
        }
    </script>
</body>
</html>
