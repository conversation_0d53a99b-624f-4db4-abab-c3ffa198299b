<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单修复测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="../styles/variables.css">
    <link rel="stylesheet" href="../styles/components.css">
    <link rel="stylesheet" href="../styles/layout.css">
    <link rel="stylesheet" href="../styles/themes.css">
    
    <style>
        body {
            padding: 20px;
            font-family: var(--font-family);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--color-border);
            border-radius: 8px;
        }
        
        .btn {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid var(--color-border);
            border-radius: 4px;
            background: var(--color-surface);
            color: var(--color-foreground);
            cursor: pointer;
        }
        
        .btn:hover {
            background: var(--color-hover);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .version-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .version-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .message-test {
            border: 1px solid var(--color-border);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: var(--color-background);
        }
    </style>
</head>
<body>
    <h1>简单修复测试</h1>
    
    <!-- 测试1：版本切换按钮 -->
    <div class="test-section">
        <h3>测试1：版本切换按钮状态</h3>
        <div class="message-test">
            <p>当前版本内容：<span id="current-content">版本1的内容</span></p>
            <div class="version-controls">
                <button class="btn version-btn" id="prev-btn" onclick="switchVersion(-1)">◀</button>
                <span id="version-info">1 / 1</span>
                <button class="btn version-btn" id="next-btn" onclick="switchVersion(1)">▶</button>
            </div>
        </div>
        <button class="btn" onclick="addVersion()">添加新版本</button>
        <button class="btn" onclick="resetVersions()">重置</button>
    </div>
    
    <!-- 测试2：事件管理器 -->
    <div class="test-section">
        <h3>测试2：事件管理器通信</h3>
        <div id="event-log" style="background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; min-height: 100px;">
            事件日志将显示在这里...
        </div>
        <button class="btn" onclick="testEventManager()">测试事件发送</button>
        <button class="btn" onclick="clearEventLog()">清空日志</button>
    </div>
    
    <!-- 测试3：通知定位 -->
    <div class="test-section">
        <h3>测试3：通知定位</h3>
        <p>点击按钮查看通知是否出现在右上角：</p>
        <button class="btn" onclick="showNotification('success')">成功通知</button>
        <button class="btn" onclick="showNotification('warning')">警告通知</button>
        <button class="btn" onclick="showNotification('error')">错误通知</button>
        <button class="btn" onclick="showNotification('info')">信息通知</button>
    </div>
    
    <!-- Toast通知容器 -->
    <div class="toast-container" id="toastContainer">
        <!-- Toast通知将在这里显示 -->
    </div>
    
    <script type="module">
        import { globalNotificationManager, NotificationTypes } from '../scripts/components/notification-manager.js';
        import { globalToolbarNotifications, ToolbarActions } from '../scripts/components/toolbar-notifications.js';
        import { EventManager } from '../scripts/utils/events.js';
        
        // 全局变量
        window.versions = ['版本1的内容'];
        window.currentVersionIndex = 0;
        window.eventManager = new EventManager();
        
        // 初始化
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('开始初始化...');
                
                // 初始化通知管理器
                await globalNotificationManager.init();
                console.log('通知管理器初始化完成');
                
                // 设置事件监听
                setupEventListeners();
                
                // 更新版本控件
                updateVersionControls();
                
                logEvent('页面初始化完成');
                
            } catch (error) {
                console.error('初始化失败:', error);
                logEvent('初始化失败: ' + error.message);
            }
        });
        
        function setupEventListeners() {
            // 监听AI回复重新生成事件
            window.eventManager.on('message:regenerate-ai-reply', (data) => {
                logEvent('收到AI回复重新生成事件: ' + JSON.stringify(data));
            });
            
            // 监听其他测试事件
            window.eventManager.on('test:event', (data) => {
                logEvent('收到测试事件: ' + JSON.stringify(data));
            });
        }
        
        function logEvent(message) {
            const log = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        // 版本控制函数
        window.addVersion = function() {
            const newVersionNumber = window.versions.length + 1;
            const newContent = `版本${newVersionNumber}的内容 - ${new Date().toLocaleTimeString()}`;
            window.versions.push(newContent);
            window.currentVersionIndex = window.versions.length - 1;
            updateVersionControls();
            logEvent(`添加了新版本: ${newContent}`);
        };
        
        window.switchVersion = function(direction) {
            const newIndex = window.currentVersionIndex + direction;
            
            if (newIndex < 0 || newIndex >= window.versions.length) {
                logEvent(`版本切换失败: 索引${newIndex}超出范围`);
                return;
            }
            
            window.currentVersionIndex = newIndex;
            updateVersionControls();
            logEvent(`切换到版本 ${newIndex + 1}`);
        };
        
        window.resetVersions = function() {
            window.versions = ['版本1的内容'];
            window.currentVersionIndex = 0;
            updateVersionControls();
            logEvent('版本已重置');
        };
        
        function updateVersionControls() {
            const versionInfo = document.getElementById('version-info');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const currentContent = document.getElementById('current-content');

            // 更新版本信息
            versionInfo.textContent = `${window.currentVersionIndex + 1} / ${window.versions.length}`;
            currentContent.textContent = window.versions[window.currentVersionIndex];

            // 更新按钮状态 - 正确处理disabled属性
            const shouldDisablePrev = window.currentVersionIndex === 0;
            const shouldDisableNext = window.currentVersionIndex === window.versions.length - 1;

            prevBtn.disabled = shouldDisablePrev;
            nextBtn.disabled = shouldDisableNext;

            // 正确设置DOM属性 - 只在需要禁用时设置属性，否则完全移除
            if (shouldDisablePrev) {
                prevBtn.setAttribute('disabled', 'disabled');
            } else {
                prevBtn.removeAttribute('disabled');
            }

            if (shouldDisableNext) {
                nextBtn.setAttribute('disabled', 'disabled');
            } else {
                nextBtn.removeAttribute('disabled');
            }

            // 记录详细的状态信息
            logEvent(`版本控件已更新: ${versionInfo.textContent}`);
            logEvent(`上一个按钮: disabled=${prevBtn.disabled}, hasAttribute=${prevBtn.hasAttribute('disabled')}`);
            logEvent(`下一个按钮: disabled=${nextBtn.disabled}, hasAttribute=${nextBtn.hasAttribute('disabled')}`);
        }
        
        // 事件管理器测试
        window.testEventManager = function() {
            // 测试发送事件
            window.eventManager.emit('test:event', {
                message: '这是一个测试事件',
                timestamp: Date.now()
            });
            
            // 测试AI回复重新生成事件
            window.eventManager.emit('message:regenerate-ai-reply', {
                userMessage: { id: 'test-msg-123' },
                content: '测试消息内容'
            });
            
            logEvent('已发送测试事件');
        };
        
        window.clearEventLog = function() {
            document.getElementById('event-log').innerHTML = '事件日志将显示在这里...';
        };
        
        // 通知测试
        window.showNotification = function(type) {
            const messages = {
                success: '这是一个成功通知测试',
                warning: '这是一个警告通知测试',
                error: '这是一个错误通知测试',
                info: '这是一个信息通知测试'
            };
            
            const actions = {
                success: ToolbarActions.SAVE,
                warning: ToolbarActions.EDIT,
                error: ToolbarActions.DELETE,
                info: ToolbarActions.SEARCH
            };
            
            try {
                switch(type) {
                    case 'success':
                        globalToolbarNotifications.notifySuccess(actions[type], messages[type]);
                        break;
                    case 'warning':
                        globalToolbarNotifications.notifyWarning(actions[type], messages[type]);
                        break;
                    case 'error':
                        globalToolbarNotifications.notifyError(actions[type], messages[type]);
                        break;
                    case 'info':
                        globalToolbarNotifications.notifyInfo(actions[type], messages[type]);
                        break;
                }
                
                logEvent(`显示了${type}类型的通知`);
                
                // 检查通知容器的位置
                setTimeout(() => {
                    const container = document.getElementById('toastContainer');
                    if (container) {
                        const rect = container.getBoundingClientRect();
                        const style = getComputedStyle(container);
                        logEvent(`通知容器位置: top=${style.top}, right=${style.right}, position=${style.position}`);
                    }
                }, 100);
                
            } catch (error) {
                logEvent(`显示通知失败: ${error.message}`);
                console.error('通知显示失败:', error);
            }
        };
        
        // 页面加载完成后的额外检查
        window.addEventListener('load', () => {
            logEvent('页面完全加载完成');
            
            // 检查关键元素
            const toastContainer = document.getElementById('toastContainer');
            if (toastContainer) {
                logEvent('Toast容器存在');
                const style = getComputedStyle(toastContainer);
                logEvent(`Toast容器样式: position=${style.position}, top=${style.top}, right=${style.right}`);
            } else {
                logEvent('Toast容器不存在！');
            }
        });
    </script>
</body>
</html>
