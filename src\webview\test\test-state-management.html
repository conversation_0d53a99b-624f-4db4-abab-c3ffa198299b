<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态管理系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #333;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background-color: #f0f0f0;
        }
        
        button.primary {
            background-color: #007acc;
            color: white;
            border-color: #007acc;
        }
        
        button.primary:hover {
            background-color: #005a9e;
        }
        
        .state-display {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log {
            background: #2d2d2d;
            color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        .warning {
            color: #ffc107;
        }
        
        input, select {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <h1>状态管理系统测试</h1>
    
    <!-- 基础状态测试 -->
    <div class="test-section">
        <h2>基础状态操作测试</h2>
        <div class="button-group">
            <button onclick="testBasicState()">测试基础状态</button>
            <button onclick="testStateSubscription()">测试状态订阅</button>
            <button onclick="testNestedState()">测试嵌套状态</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        <div id="basicStateDisplay" class="state-display"></div>
    </div>
    
    <!-- Actions测试 -->
    <div class="test-section">
        <h2>Actions和Reducer测试</h2>
        <div class="button-group">
            <button onclick="testActions()">测试Actions</button>
            <button onclick="testSettingsActions()">测试设置Actions</button>
            <button onclick="testMessageActions()">测试消息Actions</button>
            <button onclick="testUIActions()">测试UI Actions</button>
        </div>
        <div id="actionsStateDisplay" class="state-display"></div>
    </div>
    
    <!-- 中间件测试 -->
    <div class="test-section">
        <h2>中间件测试</h2>
        <div class="button-group">
            <button onclick="testMiddleware()">测试中间件</button>
            <button onclick="testPersistence()">测试持久化</button>
            <button onclick="testValidation()">测试验证</button>
        </div>
        <div id="middlewareStateDisplay" class="state-display"></div>
    </div>
    
    <!-- 持久化测试 -->
    <div class="test-section">
        <h2>本地存储测试</h2>
        <div class="button-group">
            <button onclick="testLocalStorage()">测试localStorage</button>
            <button onclick="saveToStorage()">保存到存储</button>
            <button onclick="loadFromStorage()">从存储加载</button>
            <button onclick="clearStorage()">清空存储</button>
        </div>
        <div id="storageStateDisplay" class="state-display"></div>
    </div>
    
    <!-- 实时状态显示 -->
    <div class="test-section">
        <h2>实时状态显示</h2>
        <div id="currentStateDisplay" class="state-display"></div>
    </div>
    
    <!-- 日志显示 -->
    <div class="test-section">
        <h2>操作日志</h2>
        <div id="logDisplay" class="log"></div>
    </div>
    
    <script type="module">
        import { Store, loggerMiddleware, persistenceMiddleware, validationMiddleware } from './scripts/state/store.js';
        import { ActionCreators, ActionTypes } from './scripts/state/actions.js';
        import { StorageUtils } from './scripts/utils/storage.js';
        
        // 创建测试用的store实例
        const testStore = new Store();
        
        // 添加中间件
        testStore.addMiddleware(loggerMiddleware);
        testStore.addMiddleware(persistenceMiddleware);
        testStore.addMiddleware(validationMiddleware);
        
        // 全局变量
        window.testStore = testStore;
        window.ActionCreators = ActionCreators;
        window.ActionTypes = ActionTypes;
        window.StorageUtils = StorageUtils;
        
        // 日志函数
        function log(message, type = 'info') {
            const logDisplay = document.getElementById('logDisplay');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logDisplay.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDisplay.scrollTop = logDisplay.scrollHeight;
        }
        
        // 更新状态显示
        function updateStateDisplay() {
            const currentState = testStore.getState();
            document.getElementById('currentStateDisplay').textContent = JSON.stringify(currentState, null, 2);
        }
        
        // 监听状态变化
        testStore.subscribe('*', (newState, key, value, oldValue) => {
            updateStateDisplay();
            log(`状态变化: ${key} = ${JSON.stringify(value)}`, 'info');
        });
        
        // 初始化显示
        updateStateDisplay();
        
        // 测试函数
        window.testBasicState = function() {
            log('开始基础状态测试', 'info');
            
            try {
                // 测试setState
                testStore.setState('testValue', 'Hello World');
                log('✓ setState 测试通过', 'success');
                
                // 测试getState
                const value = testStore.getState('testValue');
                if (value === 'Hello World') {
                    log('✓ getState 测试通过', 'success');
                } else {
                    log('✗ getState 测试失败', 'error');
                }
                
                // 测试批量更新
                testStore.setState({
                    test1: 'value1',
                    test2: 'value2'
                });
                log('✓ 批量更新测试通过', 'success');
                
                document.getElementById('basicStateDisplay').textContent = JSON.stringify(testStore.getState(), null, 2);
            } catch (error) {
                log(`✗ 基础状态测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testStateSubscription = function() {
            log('开始状态订阅测试', 'info');
            
            try {
                let callbackCount = 0;
                
                // 订阅特定键
                const unsubscribe = testStore.subscribe('subscriptionTest', (value) => {
                    callbackCount++;
                    log(`订阅回调被调用: ${JSON.stringify(value)}`, 'info');
                });
                
                // 触发状态变化
                testStore.setState('subscriptionTest', 'test value');
                
                if (callbackCount === 1) {
                    log('✓ 状态订阅测试通过', 'success');
                } else {
                    log('✗ 状态订阅测试失败', 'error');
                }
                
                // 取消订阅
                unsubscribe();
                testStore.setState('subscriptionTest', 'test value 2');
                
                if (callbackCount === 1) {
                    log('✓ 取消订阅测试通过', 'success');
                } else {
                    log('✗ 取消订阅测试失败', 'error');
                }
            } catch (error) {
                log(`✗ 状态订阅测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testNestedState = function() {
            log('开始嵌套状态测试', 'info');
            
            try {
                // 测试嵌套路径
                testStore.setNestedValue('nested.deep.value', 'nested test');
                const nestedValue = testStore.getNestedValue(testStore.getState(), 'nested.deep.value');
                
                if (nestedValue === 'nested test') {
                    log('✓ 嵌套状态测试通过', 'success');
                } else {
                    log('✗ 嵌套状态测试失败', 'error');
                }
            } catch (error) {
                log(`✗ 嵌套状态测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testActions = function() {
            log('开始Actions测试', 'info');
            
            try {
                // 测试dispatch
                const action = ActionCreators.setTheme('dark');
                testStore.dispatch(action);
                
                const theme = testStore.getState('settings').theme;
                if (theme === 'dark') {
                    log('✓ Actions dispatch测试通过', 'success');
                } else {
                    log('✗ Actions dispatch测试失败', 'error');
                }
                
                document.getElementById('actionsStateDisplay').textContent = JSON.stringify(testStore.getState(), null, 2);
            } catch (error) {
                log(`✗ Actions测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testSettingsActions = function() {
            log('开始设置Actions测试', 'info');
            
            try {
                testStore.dispatch(ActionCreators.setFontSize('lg'));
                testStore.dispatch(ActionCreators.setAutoScroll(false));
                testStore.dispatch(ActionCreators.updateSettings({
                    showTimestamps: false,
                    enableNotifications: false
                }));
                
                const settings = testStore.getState('settings');
                log(`设置更新完成: ${JSON.stringify(settings)}`, 'success');
            } catch (error) {
                log(`✗ 设置Actions测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testMessageActions = function() {
            log('开始消息Actions测试', 'info');
            
            try {
                const message = {
                    id: 'test-msg-1',
                    content: '测试消息',
                    isUser: true,
                    timestamp: Date.now()
                };
                
                testStore.dispatch(ActionCreators.addMessage(message));
                testStore.dispatch(ActionCreators.updateMessage('test-msg-1', { content: '更新的消息' }));
                
                const messages = testStore.getState('messages');
                log(`消息操作完成: ${messages.length} 条消息`, 'success');
            } catch (error) {
                log(`✗ 消息Actions测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testUIActions = function() {
            log('开始UI Actions测试', 'info');
            
            try {
                testStore.dispatch(ActionCreators.setLoading(true));
                testStore.dispatch(ActionCreators.setCurrentView('settings'));
                testStore.dispatch(ActionCreators.setInputValue('测试输入'));
                
                const ui = testStore.getState('ui');
                log(`UI状态更新完成: ${JSON.stringify(ui)}`, 'success');
            } catch (error) {
                log(`✗ UI Actions测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testMiddleware = function() {
            log('开始中间件测试', 'info');
            
            try {
                // 测试日志中间件
                testStore.dispatch(ActionCreators.setTheme('light'));
                log('✓ 日志中间件测试通过（检查控制台）', 'success');
                
                document.getElementById('middlewareStateDisplay').textContent = JSON.stringify(testStore.getState(), null, 2);
            } catch (error) {
                log(`✗ 中间件测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testPersistence = function() {
            log('开始持久化测试', 'info');
            
            try {
                testStore.setState('settings', {
                    theme: 'dark',
                    fontSize: 'xl',
                    autoScroll: true
                });
                
                // 检查localStorage
                const saved = localStorage.getItem('chat-settings');
                if (saved) {
                    log('✓ 持久化测试通过', 'success');
                    log(`保存的设置: ${saved}`, 'info');
                } else {
                    log('✗ 持久化测试失败', 'error');
                }
            } catch (error) {
                log(`✗ 持久化测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testValidation = function() {
            log('开始验证测试', 'info');
            
            try {
                // 测试无效主题
                testStore.setState('settings', {
                    theme: 'invalid-theme',
                    fontSize: 'invalid-size'
                });
                
                const settings = testStore.getState('settings');
                if (settings.theme === 'auto' && settings.fontSize === 'md') {
                    log('✓ 验证中间件测试通过', 'success');
                } else {
                    log('✗ 验证中间件测试失败', 'error');
                }
            } catch (error) {
                log(`✗ 验证测试失败: ${error.message}`, 'error');
            }
        };
        
        window.testLocalStorage = function() {
            log('开始localStorage测试', 'info');
            
            try {
                const testData = { test: 'localStorage test' };
                StorageUtils.safeSetItem(localStorage, 'test-key', testData);
                
                const retrieved = StorageUtils.safeGetItem(localStorage, 'test-key');
                if (retrieved && retrieved.test === 'localStorage test') {
                    log('✓ localStorage测试通过', 'success');
                } else {
                    log('✗ localStorage测试失败', 'error');
                }
                
                document.getElementById('storageStateDisplay').textContent = JSON.stringify(retrieved, null, 2);
            } catch (error) {
                log(`✗ localStorage测试失败: ${error.message}`, 'error');
            }
        };
        
        window.saveToStorage = function() {
            const currentState = testStore.getState();
            StorageUtils.safeSetItem(localStorage, 'test-state', currentState);
            log('状态已保存到localStorage', 'success');
        };
        
        window.loadFromStorage = function() {
            const savedState = StorageUtils.safeGetItem(localStorage, 'test-state');
            if (savedState) {
                testStore.restoreFromSnapshot(savedState);
                log('状态已从localStorage加载', 'success');
            } else {
                log('localStorage中没有保存的状态', 'warning');
            }
        };
        
        window.clearStorage = function() {
            localStorage.removeItem('test-state');
            localStorage.removeItem('test-key');
            localStorage.removeItem('chat-settings');
            log('localStorage已清空', 'success');
        };
        
        window.clearLog = function() {
            document.getElementById('logDisplay').innerHTML = '';
        };
        
        log('状态管理系统测试页面已加载', 'success');
    </script>
</body>
</html>
