/**
 * 设置组件
 * 提供应用设置界面和功能
 */

import BaseComponent from './base.js';
import { DOMUtils } from '../utils/dom.js';
import { StorageUtils } from '../utils/storage.js';
import { globalThemeManager } from '../utils/theme.js';
import { globalSettingsPersistence } from '../utils/settings-persistence.js';

/**
 * 设置组件类
 */
export class SettingsComponent extends BaseComponent {
  constructor(store, eventManager) {
    const settingsContainer = document.getElementById('settingsView');
    super(settingsContainer, {
      className: 'settings-component',
      autoInit: true
    });
    
    this.store = store;
    this.globalEventManager = eventManager;
    this.isVisible = false;
    
    // 绑定方法上下文
    this.show = this.show.bind(this);
    this.hide = this.hide.bind(this);
    this.toggle = this.toggle.bind(this);
    this.saveSettings = this.saveSettings.bind(this);
    this.resetSettings = this.resetSettings.bind(this);
  }

  /**
   * 获取初始状态
   */
  getInitialState() {
    return {
      ...super.getInitialState(),
      visible: false,
      settings: this.store ? (this.store.getState('settings') || {}) : {}
    };
  }

  /**
   * 初始化后钩子
   */
  async afterInit() {
    // 初始化持久化管理器
    await globalSettingsPersistence.init();

    // 监听全局事件
    this.globalEventManager.on('settings:show', this.show);
    this.globalEventManager.on('settings:hide', this.hide);
    this.globalEventManager.on('settings:toggle', this.toggle);

    // 监听设置变化
    this.store.subscribe('settings', (settings) => {
      this.setState('settings', settings, false);
      this.updateUI();
    });

    // 监听持久化管理器的设置变化
    globalSettingsPersistence.watch((data) => {
      this.handlePersistenceChange(data);
    });

    // 监听主题变化
    document.addEventListener('themechange', (e) => {
      this.updateThemeUI(e.detail.theme);
    });

    // 加载初始设置
    const initialSettings = globalSettingsPersistence.get();
    this.store.setState('settings', initialSettings);
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (!this.element) return;
    
    // 使用事件委托处理设置操作
    this.element.addEventListener('click', (e) => {
      const target = e.target;
      
      // 返回按钮
      if (target.classList.contains('settings-back-btn')) {
        this.hide();
      }
      
      // 重置按钮
      if (target.classList.contains('settings-reset-btn')) {
        this.resetSettings();
      }
      
      // 导出按钮
      if (target.classList.contains('settings-export-btn')) {
        this.exportSettings();
      }
      
      // 导入按钮
      if (target.classList.contains('settings-import-btn')) {
        this.importSettings();
      }
    });
    
    // 监听输入变化
    this.element.addEventListener('change', (e) => {
      const target = e.target;
      
      if (target.name) {
        this.handleSettingChange(target.name, target.value, target.type);
      }
    });
    
    this.element.addEventListener('input', (e) => {
      const target = e.target;
      
      if (target.name && target.type === 'range') {
        this.handleSettingChange(target.name, target.value, target.type);
      }
    });
  }

  /**
   * 执行渲染
   */
  async doRender() {
    if (!this.element) return;
    
    const settings = this.state.settings;
    
    this.element.innerHTML = `
      <div class="settings-header">
        <h1 class="settings-title">设置</h1>
        <button class="btn btn-ghost settings-back-btn">返回</button>
      </div>
      
      <div class="settings-content">
        <!-- 外观设置 -->
        <section class="settings-section">
          <h2 class="settings-section-title">外观</h2>
          
          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">主题</div>
              <div class="settings-item-description">选择应用主题</div>
            </div>
            <div class="settings-item-control">
              <select name="theme" class="input">
                <option value="auto" ${settings.theme === 'auto' ? 'selected' : ''}>跟随系统</option>
                <option value="light" ${settings.theme === 'light' ? 'selected' : ''}>浅色</option>
                <option value="dark" ${settings.theme === 'dark' ? 'selected' : ''}>深色</option>
                <option value="high-contrast" ${settings.theme === 'high-contrast' ? 'selected' : ''}>高对比度</option>
              </select>
            </div>
          </div>
          
          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">字体大小</div>
              <div class="settings-item-description">调整界面字体大小</div>
            </div>
            <div class="settings-item-control">
              <div class="flex items-center gap-md">
                <input type="range" name="fontSize" min="0" max="5" value="${this.getFontSizeIndex(settings.fontSize)}" class="input" style="flex: 1;">
                <span class="font-size-label">${this.getFontSizeLabel(settings.fontSize)}</span>
              </div>
            </div>
          </div>
        </section>
        
        <!-- 聊天设置 -->
        <section class="settings-section">
          <h2 class="settings-section-title">聊天</h2>
          
          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">自动滚动</div>
              <div class="settings-item-description">新消息时自动滚动到底部</div>
            </div>
            <div class="settings-item-control">
              <label class="switch">
                <input type="checkbox" name="autoScroll" ${settings.autoScroll ? 'checked' : ''}>
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">显示时间戳</div>
              <div class="settings-item-description">在消息中显示时间戳</div>
            </div>
            <div class="settings-item-control">
              <label class="switch">
                <input type="checkbox" name="showTimestamps" ${settings.showTimestamps ? 'checked' : ''}>
                <span class="slider"></span>
              </label>
            </div>
          </div>
          
          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">启用通知</div>
              <div class="settings-item-description">显示操作结果通知</div>
            </div>
            <div class="settings-item-control">
              <label class="switch">
                <input type="checkbox" name="enableNotifications" ${settings.enableNotifications ? 'checked' : ''}>
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">最大历史记录</div>
              <div class="settings-item-description">保存的聊天记录数量限制</div>
            </div>
            <div class="settings-item-control">
              <select name="maxHistorySize" class="input">
                <option value="500" ${settings.maxHistorySize === 500 ? 'selected' : ''}>500 条</option>
                <option value="1000" ${settings.maxHistorySize === 1000 ? 'selected' : ''}>1000 条</option>
                <option value="2000" ${settings.maxHistorySize === 2000 ? 'selected' : ''}>2000 条</option>
                <option value="5000" ${settings.maxHistorySize === 5000 ? 'selected' : ''}>5000 条</option>
              </select>
            </div>
          </div>

          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">消息折叠阈值</div>
              <div class="settings-item-description">超过多少行自动折叠消息</div>
            </div>
            <div class="settings-item-control">
              <select name="collapseThreshold" class="input">
                <option value="3" ${settings.collapseThreshold === 3 ? 'selected' : ''}>3 行</option>
                <option value="5" ${settings.collapseThreshold === 5 ? 'selected' : ''}>5 行</option>
                <option value="8" ${settings.collapseThreshold === 8 ? 'selected' : ''}>8 行</option>
                <option value="10" ${settings.collapseThreshold === 10 ? 'selected' : ''}>10 行</option>
                <option value="0" ${settings.collapseThreshold === 0 ? 'selected' : ''}>禁用折叠</option>
              </select>
            </div>
          </div>

          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">启用消息编辑</div>
              <div class="settings-item-description">允许编辑已发送的消息</div>
            </div>
            <div class="settings-item-control">
              <label class="switch">
                <input type="checkbox" name="enableMessageEdit" ${settings.enableMessageEdit !== false ? 'checked' : ''}>
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </section>

        <!-- 高级设置 -->
        <section class="settings-section">
          <h2 class="settings-section-title">高级设置</h2>

          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">调试模式</div>
              <div class="settings-item-description">启用详细的调试日志</div>
            </div>
            <div class="settings-item-control">
              <label class="switch">
                <input type="checkbox" name="debugMode" ${settings.debugMode ? 'checked' : ''}>
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">性能监控</div>
              <div class="settings-item-description">监控应用性能指标</div>
            </div>
            <div class="settings-item-control">
              <label class="switch">
                <input type="checkbox" name="performanceMonitoring" ${settings.performanceMonitoring ? 'checked' : ''}>
                <span class="slider"></span>
              </label>
            </div>
          </div>

          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">实验性功能</div>
              <div class="settings-item-description">启用实验性功能（可能不稳定）</div>
            </div>
            <div class="settings-item-control">
              <label class="switch">
                <input type="checkbox" name="experimentalFeatures" ${settings.experimentalFeatures ? 'checked' : ''}>
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </section>

        <!-- 数据管理 -->
        <section class="settings-section">
          <h2 class="settings-section-title">数据管理</h2>
          
          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">导出设置</div>
              <div class="settings-item-description">导出当前设置到文件</div>
            </div>
            <div class="settings-item-control">
              <button class="btn btn-secondary settings-export-btn">导出</button>
            </div>
          </div>
          
          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">导入设置</div>
              <div class="settings-item-description">从文件导入设置</div>
            </div>
            <div class="settings-item-control">
              <button class="btn btn-secondary settings-import-btn">导入</button>
            </div>
          </div>
          
          <div class="settings-item">
            <div class="settings-item-info">
              <div class="settings-item-label">重置设置</div>
              <div class="settings-item-description">恢复所有设置到默认值</div>
            </div>
            <div class="settings-item-control">
              <button class="btn btn-secondary settings-reset-btn">重置</button>
            </div>
          </div>
        </section>
      </div>
    `;
    
    // 添加开关样式
    this.addSwitchStyles();
  }

  /**
   * 添加开关样式
   */
  addSwitchStyles() {
    if (document.getElementById('switch-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'switch-styles';
    style.textContent = `
      .switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
      }
      
      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }
      
      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--color-border);
        transition: var(--transition-fast);
        border-radius: 24px;
      }
      
      .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: var(--transition-fast);
        border-radius: 50%;
      }
      
      input:checked + .slider {
        background-color: var(--color-primary);
      }
      
      input:checked + .slider:before {
        transform: translateX(20px);
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * 处理设置变化
   * @param {string} name - 设置名称
   * @param {string} value - 设置值
   * @param {string} type - 输入类型
   */
  handleSettingChange(name, value, type) {
    let processedValue = value;
    
    // 处理不同类型的值
    switch (type) {
      case 'checkbox':
        processedValue = document.querySelector(`[name="${name}"]`).checked;
        break;
      case 'range':
        if (name === 'fontSize') {
          const fontSizes = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
          processedValue = fontSizes[parseInt(value)];
          this.updateFontSizeLabel(processedValue);
        }
        break;
    }
    
    // 使用持久化管理器更新设置
    globalSettingsPersistence.set(name, processedValue);

    // 应用特定设置
    this.applySettingChange(name, processedValue);

    this.emit('settingChanged', { name, value: processedValue });
  }

  /**
   * 处理持久化变化
   * @param {object} data - 变化数据
   */
  handlePersistenceChange(data) {
    const { key, newValue } = data;

    // 更新本地状态
    const currentSettings = this.state.settings;
    const newSettings = { ...currentSettings, [key]: newValue };

    this.setState('settings', newSettings, false);
    this.store.setState('settings', newSettings);

    // 更新UI
    this.updateSettingUI(key, newValue);

    // 应用设置变化
    this.applySettingChange(key, newValue);
  }

  /**
   * 更新设置UI
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   */
  updateSettingUI(key, value) {
    if (!this.element) return;

    const input = this.element.querySelector(`[name="${key}"]`);
    if (input) {
      if (input.type === 'checkbox') {
        input.checked = value;
      } else if (key === 'fontSize') {
        input.value = this.getFontSizeIndex(value);
        this.updateFontSizeLabel(value);
      } else {
        input.value = value;
      }
    }
  }

  /**
   * 应用设置变化
   * @param {string} name - 设置名称
   * @param {any} value - 设置值
   */
  applySettingChange(name, value) {
    switch (name) {
      case 'theme':
        globalThemeManager.setTheme(value);
        break;
      case 'fontSize':
        document.documentElement.setAttribute('data-font-size', value);
        break;
    }
  }

  /**
   * 更新字体大小标签
   * @param {string} fontSize - 字体大小
   */
  updateFontSizeLabel(fontSize) {
    const label = this.element?.querySelector('.font-size-label');
    if (label) {
      label.textContent = this.getFontSizeLabel(fontSize);
    }
  }

  /**
   * 获取字体大小索引
   * @param {string} fontSize - 字体大小
   * @returns {number} 索引
   */
  getFontSizeIndex(fontSize) {
    const fontSizes = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
    return fontSizes.indexOf(fontSize);
  }

  /**
   * 获取字体大小标签
   * @param {string} fontSize - 字体大小
   * @returns {string} 标签
   */
  getFontSizeLabel(fontSize) {
    const labels = {
      xs: '极小',
      sm: '小',
      md: '中等',
      lg: '大',
      xl: '极大',
      '2xl': '超大'
    };
    return labels[fontSize] || '中等';
  }

  /**
   * 显示设置页面
   */
  show() {
    this.isVisible = true;
    this.setState('visible', true);
    
    // 隐藏聊天视图
    const chatView = document.getElementById('chatView');
    if (chatView) {
      DOMUtils.addClass(chatView, 'hidden');
    }
    
    // 显示设置视图
    if (this.element) {
      DOMUtils.removeClass(this.element, 'hidden');
    }
    
    this.emit('shown');
  }

  /**
   * 隐藏设置页面
   */
  hide() {
    this.isVisible = false;
    this.setState('visible', false);
    
    // 显示聊天视图
    const chatView = document.getElementById('chatView');
    if (chatView) {
      DOMUtils.removeClass(chatView, 'hidden');
    }
    
    // 隐藏设置视图
    if (this.element) {
      DOMUtils.addClass(this.element, 'hidden');
    }
    
    this.emit('hidden');
  }

  /**
   * 切换设置页面显示
   */
  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 保存设置
   */
  saveSettings() {
    const settings = this.state.settings;
    StorageUtils.saveSettings(settings);
    
    this.globalEventManager.emit('toast:show', {
      message: '设置已保存',
      type: 'success'
    });
  }

  /**
   * 重置设置
   */
  resetSettings() {
    const defaultSettings = StorageUtils.DEFAULT_SETTINGS;
    
    this.setState('settings', defaultSettings);
    this.store.setState('settings', defaultSettings);
    StorageUtils.saveSettings(defaultSettings);
    
    // 重新渲染
    this.render();
    
    this.globalEventManager.emit('toast:show', {
      message: '设置已重置',
      type: 'success'
    });
  }

  /**
   * 导出设置
   */
  exportSettings() {
    const data = StorageUtils.exportData();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-settings-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
    
    this.globalEventManager.emit('toast:show', {
      message: '设置已导出',
      type: 'success'
    });
  }

  /**
   * 导入设置
   */
  importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result);
          
          if (StorageUtils.importData(data)) {
            // 重新加载设置
            const newSettings = StorageUtils.getSettings();
            this.setState('settings', newSettings);
            this.store.setState('settings', newSettings);
            
            // 重新渲染
            this.render();
            
            this.globalEventManager.emit('toast:show', {
              message: '设置已导入',
              type: 'success'
            });
          } else {
            throw new Error('导入失败');
          }
        } catch (error) {
          console.error('Import settings error:', error);
          this.globalEventManager.emit('toast:show', {
            message: '导入失败，请检查文件格式',
            type: 'error'
          });
        }
      };
      
      reader.readAsText(file);
    };
    
    input.click();
  }

  /**
   * 更新UI
   */
  updateUI() {
    if (!this.element || !this.isVisible) return;
    
    const settings = this.state.settings;
    
    // 更新表单值
    Object.entries(settings).forEach(([key, value]) => {
      const input = this.element.querySelector(`[name="${key}"]`);
      if (input) {
        if (input.type === 'checkbox') {
          input.checked = value;
        } else if (key === 'fontSize') {
          input.value = this.getFontSizeIndex(value);
          this.updateFontSizeLabel(value);
        } else {
          input.value = value;
        }
      }
    });
  }

  /**
   * 更新主题UI
   * @param {string} theme - 主题
   */
  updateThemeUI(theme) {
    const themeSelect = this.element?.querySelector('[name="theme"]');
    if (themeSelect) {
      themeSelect.value = theme;
    }
  }

  /**
   * 销毁前钩子
   */
  beforeDestroy() {
    // 移除全局事件监听
    this.globalEventManager.off('settings:show', this.show);
    this.globalEventManager.off('settings:hide', this.hide);
    this.globalEventManager.off('settings:toggle', this.toggle);
  }
}

export default SettingsComponent;
