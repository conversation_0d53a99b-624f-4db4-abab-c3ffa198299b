<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据迁移测试 - </title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.danger {
            background: #d73a49;
        }
        .button.danger:hover {
            background: #b31d28;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007acc;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据迁移测试 - </h1>
        <p>测试新的消息数据结构和迁移功能</p>

        <div class="test-section">
            <h3>1. 创建测试数据</h3>
            <button class="button" onclick="createLegacyTestData()">创建旧格式测试数据</button>
            <button class="button" onclick="createNewFormatTestData()">创建新格式测试数据</button>
            <button class="button danger" onclick="clearAllData()">清空所有数据</button>
            <div id="createDataResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 数据迁移测试</h3>
            <button class="button" onclick="testDataMigration()">执行数据迁移</button>
            <button class="button" onclick="forceMigration()">强制重新迁移</button>
            <button class="button" onclick="checkMigrationStatus()">检查迁移状态</button>
            <div id="migrationResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 数据验证测试</h3>
            <button class="button" onclick="validateCurrentData()">验证当前数据</button>
            <button class="button" onclick="showDataStructure()">显示数据结构</button>
            <button class="button" onclick="compareDataFormats()">对比新旧格式</button>
            <div id="validationResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 统计信息</h3>
            <div class="stats" id="statisticsContainer">
                <div class="stat-card">
                    <div class="stat-number" id="totalMessages">0</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="userMessages">0</div>
                    <div class="stat-label">用户消息</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="aiReplies">0</div>
                    <div class="stat-label">AI回复</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalVersions">0</div>
                    <div class="stat-label">总版本数</div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { migrateMessagesToNewFormat, restoreFromBackup, clearBackup } from '../scripts/utils/data-migration.js';
        import { createMessage, createAIReply, validateMessage, AIReplyStatus } from '../scripts/types/message-types.js';
        import { StorageUtils } from '../scripts/utils/storage.js';

        // 全局函数，供HTML调用
        window.createLegacyTestData = createLegacyTestData;
        window.createNewFormatTestData = createNewFormatTestData;
        window.clearAllData = clearAllData;
        window.testDataMigration = testDataMigration;
        window.forceMigration = forceMigration;
        window.checkMigrationStatus = checkMigrationStatus;
        window.validateCurrentData = validateCurrentData;
        window.showDataStructure = showDataStructure;
        window.compareDataFormats = compareDataFormats;

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        function createLegacyTestData() {
            const legacyMessages = [
                {
                    id: 'msg_1',
                    content: '你好，我是用户消息1',
                    isUser: true,
                    timestamp: Date.now() - 10000,
                    edited: false,
                    collapsed: false,
                    versions: [{
                        content: '你好，我是用户消息1',
                        timestamp: Date.now() - 10000,
                        aiReply: '你好！我是AI助手，很高兴为您服务。'
                    }],
                    currentVersionIndex: 0
                },
                {
                    id: 'ai_1',
                    content: '你好！我是AI助手，很高兴为您服务。',
                    isUser: false,
                    timestamp: Date.now() - 9000
                },
                {
                    id: 'msg_2',
                    content: '请帮我解决一个问题',
                    isUser: true,
                    timestamp: Date.now() - 8000,
                    edited: true,
                    versions: [
                        {
                            content: '请帮我解决一个问题',
                            timestamp: Date.now() - 8000,
                            aiReply: null
                        },
                        {
                            content: '请帮我解决一个编程问题',
                            timestamp: Date.now() - 7000,
                            aiReply: '当然可以！请告诉我您遇到的具体编程问题。'
                        }
                    ],
                    currentVersionIndex: 1
                },
                {
                    id: 'ai_2',
                    content: '当然可以！请告诉我您遇到的具体编程问题。',
                    isUser: false,
                    timestamp: Date.now() - 6000
                }
            ];

            localStorage.setItem('chat-history', JSON.stringify(legacyMessages));
            StorageUtils.setDataVersion('legacy');
            
            showResult('createDataResult', '已创建旧格式测试数据:\n' + JSON.stringify(legacyMessages, null, 2), 'success');
            updateStatistics();
        }

        function createNewFormatTestData() {
            const newMessages = [
                createMessage('这是新格式的用户消息', true),
                createMessage('这是另一条新格式消息', true)
            ];

            // 为第一条消息添加AI回复
            newMessages[0].versions[0].aiReply = createAIReply('这是新格式的AI回复');

            StorageUtils.saveChatHistory(newMessages);
            
            showResult('createDataResult', '已创建新格式测试数据:\n' + JSON.stringify(newMessages, null, 2), 'success');
            updateStatistics();
        }

        function clearAllData() {
            localStorage.clear();
            showResult('createDataResult', '所有数据已清空', 'warning');
            updateStatistics();
        }

        function testDataMigration() {
            try {
                const messages = StorageUtils.getChatHistory();
                const migrationStatus = StorageUtils.getMigrationStatus();
                
                showResult('migrationResult', 
                    `迁移完成!\n` +
                    `消息数量: ${messages.length}\n` +
                    `迁移状态: ${JSON.stringify(migrationStatus, null, 2)}\n` +
                    `迁移后数据:\n${JSON.stringify(messages, null, 2)}`, 
                    'success'
                );
                updateStatistics();
            } catch (error) {
                showResult('migrationResult', `迁移失败: ${error.message}`, 'error');
            }
        }

        function forceMigration() {
            try {
                const messages = StorageUtils.forceMigration();
                showResult('migrationResult', 
                    `强制迁移完成!\n` +
                    `消息数量: ${messages.length}\n` +
                    `数据:\n${JSON.stringify(messages, null, 2)}`, 
                    'success'
                );
                updateStatistics();
            } catch (error) {
                showResult('migrationResult', `强制迁移失败: ${error.message}`, 'error');
            }
        }

        function checkMigrationStatus() {
            const status = StorageUtils.getMigrationStatus();
            const version = StorageUtils.getDataVersion();
            
            showResult('migrationResult', 
                `数据版本: ${version}\n` +
                `迁移状态:\n${JSON.stringify(status, null, 2)}`, 
                'info'
            );
        }

        function validateCurrentData() {
            try {
                const messages = StorageUtils.getChatHistory();
                const results = [];
                
                messages.forEach((message, index) => {
                    const validation = validateMessage(message);
                    results.push(`消息 ${index}: ${validation.isValid ? '✓ 有效' : '✗ 无效'}`);
                    
                    if (validation.errors.length > 0) {
                        results.push(`  错误: ${validation.errors.join(', ')}`);
                    }
                    
                    if (validation.warnings.length > 0) {
                        results.push(`  警告: ${validation.warnings.join(', ')}`);
                    }
                });
                
                showResult('validationResult', results.join('\n'), 'info');
            } catch (error) {
                showResult('validationResult', `验证失败: ${error.message}`, 'error');
            }
        }

        function showDataStructure() {
            const messages = StorageUtils.getChatHistory();
            const structure = messages.map(msg => ({
                id: msg.id,
                isUser: msg.isUser,
                versionsCount: msg.versions?.length || 0,
                currentVersion: msg.currentVersionIndex,
                hasAIReply: msg.versions?.[msg.currentVersionIndex]?.aiReply ? true : false
            }));
            
            showResult('validationResult', 
                `数据结构概览:\n${JSON.stringify(structure, null, 2)}`, 
                'info'
            );
        }

        function compareDataFormats() {
            const legacyData = JSON.parse(localStorage.getItem('chat-history') || '[]');
            const newData = StorageUtils.getChatHistory();
            
            showResult('validationResult', 
                `旧格式数据 (${legacyData.length} 条):\n${JSON.stringify(legacyData, null, 2)}\n\n` +
                `新格式数据 (${newData.length} 条):\n${JSON.stringify(newData, null, 2)}`, 
                'info'
            );
        }

        function updateStatistics() {
            try {
                const messages = StorageUtils.getChatHistory();
                let totalVersions = 0;
                let aiRepliesCount = 0;
                
                messages.forEach(msg => {
                    if (msg.versions) {
                        totalVersions += msg.versions.length;
                        aiRepliesCount += msg.versions.filter(v => v.aiReply).length;
                    }
                });
                
                document.getElementById('totalMessages').textContent = messages.length;
                document.getElementById('userMessages').textContent = messages.filter(m => m.isUser).length;
                document.getElementById('aiReplies').textContent = aiRepliesCount;
                document.getElementById('totalVersions').textContent = totalVersions;
            } catch (error) {
                console.error('更新统计信息失败:', error);
            }
        }

        // 页面加载时更新统计信息
        document.addEventListener('DOMContentLoaded', updateStatistics);
    </script>
</body>
</html>
