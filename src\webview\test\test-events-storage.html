<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事件和存储测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.success {
            background: #28a745;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .event-log {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .event-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .event-entry.message {
            color: #63b3ed;
        }
        .event-entry.ai-reply {
            color: #68d391;
        }
        .event-entry.version {
            color: #fbb6ce;
        }
        .event-entry.storage {
            color: #f6e05e;
        }
        .state-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .state-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .state-item:last-child {
            border-bottom: none;
        }
        .state-label {
            font-weight: bold;
        }
        .state-value {
            color: #666;
        }
        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007acc;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>事件和存储测试</h1>
        <p>测试新的事件系统和状态管理功能</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 事件系统测试</h3>
            <div class="controls">
                <button class="button" onclick="testEventEmission()">测试事件发射</button>
                <button class="button" onclick="testEventValidation()">测试事件验证</button>
                <button class="button" onclick="testEventHistory()">测试事件历史</button>
                <button class="button" onclick="testBatchEvents()">测试批量事件</button>
            </div>
            <div id="eventResult" class="result"></div>
            <div id="eventLog" class="event-log" style="display: none;">
                <h4 style="color: #e2e8f0; margin-top: 0;">事件日志</h4>
                <div id="eventLogContent"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. 状态管理测试</h3>
            <div class="controls">
                <button class="button" onclick="testStateUpdates()">测试状态更新</button>
                <button class="button" onclick="testAIReplyState()">测试AI回复状态</button>
                <button class="button" onclick="testVersionState()">测试版本状态</button>
                <button class="button" onclick="testStateValidation()">测试状态验证</button>
            </div>
            <div id="stateResult" class="result"></div>
            <div id="stateDisplay" class="state-display" style="display: none;">
                <h4>当前状态</h4>
                <div id="stateContent"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>3. 存储集成测试</h3>
            <div class="controls">
                <button class="button" onclick="testStoragePersistence()">测试存储持久化</button>
                <button class="button" onclick="testMigrationState()">测试迁移状态</button>
                <button class="button" onclick="testStateCleanup()">测试状态清理</button>
                <button class="button" onclick="testBackupIntegration()">测试备份集成</button>
            </div>
            <div id="storageResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 应用统计</h3>
            <div class="controls">
                <button class="button" onclick="showStatistics()">显示统计信息</button>
                <button class="button" onclick="updateStatistics()">更新统计</button>
                <button class="button" onclick="resetStatistics()">重置统计</button>
            </div>
            <div id="statisticsDisplay" class="statistics-grid" style="display: none;"></div>
            <div id="statisticsResult" class="result"></div>
        </div>
    </div>

    <script type="module">
        // 模拟事件系统
        const SchemeB_Events = {
            MESSAGE: {
                ADDED: 'message:added',
                UPDATED: 'message:updated',
                DELETED: 'message:deleted',
                COPIED: 'message:copied'
            },
            AI_REPLY: {
                GENERATION_START: 'ai-reply:generation:start',
                GENERATION_PROGRESS: 'ai-reply:generation:progress',
                GENERATION_COMPLETE: 'ai-reply:generation:complete',
                STATUS_CHANGED: 'ai-reply:status:changed'
            },
            VERSION: {
                SWITCHED: 'version:switched',
                CREATED: 'version:created',
                DELETED: 'version:deleted'
            },
            STORAGE: {
                SAVE_COMPLETE: 'storage:save:complete',
                MIGRATION_COMPLETE: 'storage:migration:complete'
            }
        };

        // 模拟事件管理器
        class TestEventManager {
            constructor() {
                this.listeners = new Map();
                this.eventHistory = [];
                this.enableValidation = true;
            }

            on(event, callback) {
                if (!this.listeners.has(event)) {
                    this.listeners.set(event, []);
                }
                this.listeners.get(event).push(callback);
            }

            emit(event, ...args) {
                // 记录事件
                this.recordEvent(event, args);
                
                // 触发监听器
                if (this.listeners.has(event)) {
                    this.listeners.get(event).forEach(callback => {
                        try {
                            callback(...args);
                        } catch (error) {
                            console.error('Event listener error:', error);
                        }
                    });
                }

                // 更新事件日志显示
                this.updateEventLog();
                return true;
            }

            recordEvent(event, args) {
                this.eventHistory.push({
                    event,
                    args: JSON.parse(JSON.stringify(args)),
                    timestamp: Date.now(),
                    id: Math.random().toString(36).substring(2, 11)
                });

                // 限制历史记录大小
                if (this.eventHistory.length > 50) {
                    this.eventHistory.shift();
                }
            }

            getEventHistory(filter = null) {
                let history = this.eventHistory;
                if (filter) {
                    history = history.filter(record => record.event.includes(filter));
                }
                return history;
            }

            updateEventLog() {
                const logElement = document.getElementById('eventLogContent');
                if (logElement) {
                    const recentEvents = this.eventHistory.slice(-20);
                    logElement.innerHTML = recentEvents.map(record => {
                        const time = new Date(record.timestamp).toLocaleTimeString();
                        const category = record.event.split(':')[0];
                        return `<div class="event-entry ${category}">[${time}] ${record.event}</div>`;
                    }).join('');
                }
            }

            emitBatch(events) {
                events.forEach(({ event, args = [] }) => {
                    this.emit(event, ...args);
                });
            }
        }

        // 模拟状态管理器
        class TestStateManager {
            constructor() {
                this.state = {
                    messages: [],
                    aiReplies: {
                        activeReplies: new Map(),
                        statistics: {
                            totalGenerated: 0,
                            totalErrors: 0,
                            averageResponseTime: 0
                        }
                    },
                    versions: {
                        currentVersions: new Map(),
                        versionHistory: new Map()
                    },
                    app: {
                        migrationStatus: 'completed',
                        dataVersion: '2.0',
                        backupCount: 0,
                        lastActivity: Date.now()
                    }
                };
            }

            updateAIReplyState(messageId, aiReply) {
                if (aiReply && ['pending', 'generating'].includes(aiReply.status)) {
                    this.state.aiReplies.activeReplies.set(messageId, aiReply);
                } else {
                    this.state.aiReplies.activeReplies.delete(messageId);
                    
                    if (aiReply && aiReply.status === 'completed') {
                        this.state.aiReplies.statistics.totalGenerated++;
                    } else if (aiReply && aiReply.status === 'error') {
                        this.state.aiReplies.statistics.totalErrors++;
                    }
                }
            }

            updateVersionState(messageId, versionIndex) {
                this.state.versions.currentVersions.set(messageId, versionIndex);
            }

            updateMigrationState(status, details = {}) {
                this.state.app.migrationStatus = status;
                Object.assign(this.state.app, details);
            }

            getAppStatistics() {
                return {
                    totalMessages: this.state.messages.length,
                    totalAIReplies: this.state.aiReplies.statistics.totalGenerated,
                    activeAIReplies: this.state.aiReplies.activeReplies.size,
                    errorRate: this.state.aiReplies.statistics.totalErrors,
                    migrationStatus: this.state.app.migrationStatus,
                    dataVersion: this.state.app.dataVersion,
                    backupCount: this.state.app.backupCount
                };
            }

            validateState() {
                return {
                    isValid: true,
                    errors: [],
                    warnings: []
                };
            }

            cleanupExpiredState() {
                // 模拟清理过期状态
                console.log('状态清理完成');
            }
        }

        // 全局实例
        const eventManager = new TestEventManager();
        const stateManager = new TestStateManager();

        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 导出测试函数到全局作用域
        window.testEventEmission = testEventEmission;
        window.testEventValidation = testEventValidation;
        window.testEventHistory = testEventHistory;
        window.testBatchEvents = testBatchEvents;
        window.testStateUpdates = testStateUpdates;
        window.testAIReplyState = testAIReplyState;
        window.testVersionState = testVersionState;
        window.testStateValidation = testStateValidation;
        window.testStoragePersistence = testStoragePersistence;
        window.testMigrationState = testMigrationState;
        window.testStateCleanup = testStateCleanup;
        window.testBackupIntegration = testBackupIntegration;
        window.showStatistics = showStatistics;
        window.updateStatistics = updateStatistics;
        window.resetStatistics = resetStatistics;

        // 1. 事件系统测试
        function testEventEmission() {
            // 测试各种事件类型
            eventManager.emit(SchemeB_Events.MESSAGE.ADDED, {
                message: { id: 'msg1', content: '测试消息', isUser: true }
            });

            eventManager.emit(SchemeB_Events.AI_REPLY.GENERATION_START, {
                messageId: 'msg1',
                aiReply: { id: 'ai1', status: 'generating' }
            });

            eventManager.emit(SchemeB_Events.VERSION.SWITCHED, {
                messageId: 'msg1',
                fromVersion: 0,
                toVersion: 1
            });

            const logElement = document.getElementById('eventLog');
            logElement.style.display = 'block';

            showResult('eventResult', '✅ 事件发射测试完成\n已发射3个不同类型的事件', 'success');
        }

        function testEventValidation() {
            let validEvents = 0;
            let invalidEvents = 0;

            // 测试有效事件
            const validEventTypes = Object.values(SchemeB_Events).flatMap(category => Object.values(category));
            validEventTypes.forEach(eventType => {
                if (eventManager.emit(eventType, { test: true })) {
                    validEvents++;
                }
            });

            // 测试无效事件
            try {
                eventManager.emit('invalid:event', { test: true });
                invalidEvents++;
            } catch (error) {
                // 预期的错误
            }

            showResult('eventResult', `✅ 事件验证测试完成\n有效事件: ${validEvents}\n无效事件: ${invalidEvents}`, 'success');
        }

        function testEventHistory() {
            // 清空历史
            eventManager.eventHistory = [];

            // 发射一系列事件
            for (let i = 0; i < 5; i++) {
                eventManager.emit(SchemeB_Events.MESSAGE.ADDED, { messageId: `msg${i}` });
                eventManager.emit(SchemeB_Events.AI_REPLY.GENERATION_COMPLETE, { messageId: `msg${i}` });
            }

            const history = eventManager.getEventHistory();
            const messageEvents = eventManager.getEventHistory('message');

            showResult('eventResult', `✅ 事件历史测试完成\n总事件数: ${history.length}\n消息事件数: ${messageEvents.length}`, 'success');
        }

        function testBatchEvents() {
            const batchEvents = [
                { event: SchemeB_Events.MESSAGE.ADDED, args: [{ messageId: 'batch1' }] },
                { event: SchemeB_Events.AI_REPLY.GENERATION_START, args: [{ messageId: 'batch1' }] },
                { event: SchemeB_Events.AI_REPLY.GENERATION_COMPLETE, args: [{ messageId: 'batch1' }] },
                { event: SchemeB_Events.VERSION.CREATED, args: [{ messageId: 'batch1', versionId: 'v1' }] }
            ];

            eventManager.emitBatch(batchEvents);

            showResult('eventResult', `✅ 批量事件测试完成\n已发射 ${batchEvents.length} 个批量事件`, 'success');
        }

        // 2. 状态管理测试
        function testStateUpdates() {
            // 添加测试消息
            stateManager.state.messages.push({
                id: 'msg1',
                content: '测试消息',
                isUser: true,
                versions: [{ id: 'v1', content: '测试消息' }]
            });

            // 更新状态
            stateManager.state.app.lastActivity = Date.now();

            const stateDisplay = document.getElementById('stateDisplay');
            const stateContent = document.getElementById('stateContent');

            stateContent.innerHTML = `
                <div class="state-item">
                    <span class="state-label">消息数量:</span>
                    <span class="state-value">${stateManager.state.messages.length}</span>
                </div>
                <div class="state-item">
                    <span class="state-label">数据版本:</span>
                    <span class="state-value">${stateManager.state.app.dataVersion}</span>
                </div>
                <div class="state-item">
                    <span class="state-label">迁移状态:</span>
                    <span class="state-value">${stateManager.state.app.migrationStatus}</span>
                </div>
            `;

            stateDisplay.style.display = 'block';

            showResult('stateResult', '✅ 状态更新测试完成\n状态已更新并显示', 'success');
        }

        function testAIReplyState() {
            // 测试AI回复状态管理
            stateManager.updateAIReplyState('msg1', {
                id: 'ai1',
                status: 'generating',
                timestamp: Date.now()
            });

            stateManager.updateAIReplyState('msg2', {
                id: 'ai2',
                status: 'completed',
                timestamp: Date.now()
            });

            stateManager.updateAIReplyState('msg3', {
                id: 'ai3',
                status: 'error',
                timestamp: Date.now()
            });

            const stats = stateManager.getAppStatistics();

            showResult('stateResult', `✅ AI回复状态测试完成\n活跃回复: ${stats.activeAIReplies}\n已完成回复: ${stats.totalAIReplies}\n错误数: ${stats.errorRate}`, 'success');
        }

        function testVersionState() {
            // 测试版本状态管理
            stateManager.updateVersionState('msg1', 0);
            stateManager.updateVersionState('msg2', 1);
            stateManager.updateVersionState('msg3', 2);

            const currentVersions = Array.from(stateManager.state.versions.currentVersions.entries());

            showResult('stateResult', `✅ 版本状态测试完成\n管理的版本数: ${currentVersions.length}\n版本映射: ${currentVersions.map(([id, v]) => `${id}:v${v}`).join(', ')}`, 'success');
        }

        function testStateValidation() {
            const validation = stateManager.validateState();

            showResult('stateResult', `✅ 状态验证测试完成\n验证结果: ${validation.isValid ? '通过' : '失败'}\n错误数: ${validation.errors.length}\n警告数: ${validation.warnings.length}`, validation.isValid ? 'success' : 'error');
        }

        // 3. 存储集成测试
        function testStoragePersistence() {
            // 模拟存储持久化
            const testData = {
                messages: stateManager.state.messages,
                settings: { theme: 'dark', fontSize: 'md' },
                timestamp: Date.now()
            };

            try {
                localStorage.setItem('test-chat-data', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('test-chat-data'));

                eventManager.emit(SchemeB_Events.STORAGE.SAVE_COMPLETE, {
                    dataSize: JSON.stringify(testData).length,
                    timestamp: Date.now()
                });

                showResult('storageResult', `✅ 存储持久化测试完成\n数据大小: ${JSON.stringify(testData).length} 字节\n存储成功: 是`, 'success');
            } catch (error) {
                showResult('storageResult', `❌ 存储持久化测试失败\n错误: ${error.message}`, 'error');
            }
        }

        function testMigrationState() {
            // 测试迁移状态管理
            stateManager.updateMigrationState('in-progress', {
                dataVersion: '2.0',
                backupCount: 1,
                lastBackupTime: Date.now()
            });

            setTimeout(() => {
                stateManager.updateMigrationState('completed', {
                    backupCount: 2
                });

                eventManager.emit(SchemeB_Events.STORAGE.MIGRATION_COMPLETE, {
                    fromVersion: '1.0',
                    toVersion: '2.0',
                    duration: 1000
                });

                const stats = stateManager.getAppStatistics();
                showResult('storageResult', `✅ 迁移状态测试完成\n迁移状态: ${stats.migrationStatus}\n数据版本: ${stats.dataVersion}\n备份数: ${stats.backupCount}`, 'success');
            }, 1000);

            showResult('storageResult', '🔄 迁移状态测试进行中...', 'info');
        }

        function testStateCleanup() {
            // 添加一些过期状态
            stateManager.state.aiReplies.activeReplies.set('expired1', {
                id: 'ai_expired',
                status: 'generating',
                timestamp: Date.now() - 25 * 60 * 60 * 1000 // 25小时前
            });

            const beforeCleanup = stateManager.state.aiReplies.activeReplies.size;

            stateManager.cleanupExpiredState();

            const afterCleanup = stateManager.state.aiReplies.activeReplies.size;

            showResult('storageResult', `✅ 状态清理测试完成\n清理前: ${beforeCleanup} 个活跃回复\n清理后: ${afterCleanup} 个活跃回复`, 'success');
        }

        function testBackupIntegration() {
            // 模拟备份集成
            const backupData = {
                state: stateManager.state,
                timestamp: Date.now(),
                version: '2.0'
            };

            try {
                localStorage.setItem('test-backup', JSON.stringify(backupData));
                stateManager.updateMigrationState('completed', {
                    backupCount: stateManager.state.app.backupCount + 1,
                    lastBackupTime: Date.now()
                });

                showResult('storageResult', `✅ 备份集成测试完成\n备份大小: ${JSON.stringify(backupData).length} 字节\n备份时间: ${new Date().toLocaleTimeString()}`, 'success');
            } catch (error) {
                showResult('storageResult', `❌ 备份集成测试失败\n错误: ${error.message}`, 'error');
            }
        }

        // 4. 应用统计
        function showStatistics() {
            const stats = stateManager.getAppStatistics();
            const statisticsDisplay = document.getElementById('statisticsDisplay');

            statisticsDisplay.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${stats.totalMessages}</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.totalAIReplies}</div>
                    <div class="stat-label">AI回复数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.activeAIReplies}</div>
                    <div class="stat-label">活跃回复</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.errorRate}</div>
                    <div class="stat-label">错误数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.migrationStatus}</div>
                    <div class="stat-label">迁移状态</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.dataVersion}</div>
                    <div class="stat-label">数据版本</div>
                </div>
            `;

            statisticsDisplay.style.display = 'grid';
            showResult('statisticsResult', '✅ 统计信息已显示', 'success');
        }

        function updateStatistics() {
            // 模拟统计更新
            stateManager.state.aiReplies.statistics.totalGenerated += 5;
            stateManager.state.aiReplies.statistics.totalErrors += 1;
            stateManager.state.app.lastActivity = Date.now();

            showStatistics();
            showResult('statisticsResult', '✅ 统计信息已更新', 'success');
        }

        function resetStatistics() {
            // 重置统计
            stateManager.state.aiReplies.statistics = {
                totalGenerated: 0,
                totalErrors: 0,
                averageResponseTime: 0
            };
            stateManager.state.messages = [];
            stateManager.state.aiReplies.activeReplies.clear();

            showStatistics();
            showResult('statisticsResult', '✅ 统计信息已重置', 'info');
        }

        // 初始化一些测试数据
        stateManager.state.messages.push({
            id: 'init1',
            content: '初始化测试消息',
            isUser: true,
            versions: [{ id: 'v1', content: '初始化测试消息' }]
        });

        // 设置事件监听器
        eventManager.on('*', (event, ...args) => {
            console.log(`Event: ${event}`, args);
        });
    </script>
</body>
</html>
    </script>
</body>
</html>
