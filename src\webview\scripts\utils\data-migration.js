/**
 * 数据迁移工具
 * 用于将旧版本的消息数据结构迁移到新的结构
 */

import { 
  createMessage, 
  createMessageVersion, 
  createAIReply, 
  validateMessage,
  AIReplyStatus 
} from '../types/message-types.js';

/**
 * 数据迁移主函数
 * @param {Array} oldMessages - 旧格式的消息数组
 * @returns {Object} 迁移结果
 */
export function migrateMessagesToNewFormat(oldMessages) {
  console.log('开始数据迁移，旧消息数量:', oldMessages.length);
  
  const migrationResult = {
    messages: [],
    statistics: {
      totalMessages: oldMessages.length,
      userMessages: 0,
      aiReplies: 0,
      migratedVersions: 0,
      orphanedAIMessages: 0
    },
    warnings: [],
    errors: []
  };

  try {
    // 创建备份
    const backup = createBackup(oldMessages);
    console.log('已创建数据备份');

    // 执行迁移
    const newMessages = performMigration(oldMessages, migrationResult);
    
    // 验证迁移结果
    const validationResults = validateMigratedData(newMessages);
    migrationResult.messages = newMessages;
    migrationResult.warnings.push(...validationResults.warnings);
    migrationResult.errors.push(...validationResults.errors);

    console.log('数据迁移完成，统计信息:', migrationResult.statistics);
    
    return migrationResult;
  } catch (error) {
    migrationResult.errors.push(`迁移过程中发生错误: ${error.message}`);
    console.error('数据迁移失败:', error);
    return migrationResult;
  }
}

/**
 * 执行实际的数据迁移
 * @param {Array} oldMessages - 旧消息数组
 * @param {Object} migrationResult - 迁移结果对象
 * @returns {Array} 新格式的消息数组
 */
function performMigration(oldMessages, migrationResult) {
  const newMessages = [];
  
  for (let i = 0; i < oldMessages.length; i++) {
    const message = oldMessages[i];
    
    // 只处理用户消息，AI消息将被合并到用户消息中
    if (message.isUser) {
      try {
        const migratedMessage = migrateUserMessage(message, oldMessages, i, migrationResult);
        newMessages.push(migratedMessage);
        migrationResult.statistics.userMessages++;
      } catch (error) {
        migrationResult.errors.push(`迁移用户消息 ${message.id} 时出错: ${error.message}`);
      }
    } else {
      // 检查是否为孤立的AI消息
      if (!isAIMessageLinkedToUser(message, oldMessages, i)) {
        migrationResult.statistics.orphanedAIMessages++;
        migrationResult.warnings.push(`发现孤立的AI消息: ${message.id}`);
      }
    }
  }
  
  return newMessages;
}

/**
 * 迁移单个用户消息
 * @param {Object} userMessage - 用户消息
 * @param {Array} allMessages - 所有消息数组
 * @param {number} messageIndex - 消息在数组中的索引
 * @param {Object} migrationResult - 迁移结果对象
 * @returns {Object} 迁移后的消息
 */
function migrateUserMessage(userMessage, allMessages, messageIndex, migrationResult) {
  // 创建新的消息结构
  const newMessage = {
    id: userMessage.id,
    isUser: true,
    timestamp: userMessage.timestamp,
    edited: userMessage.edited || false,
    collapsed: userMessage.collapsed || false,
    type: userMessage.type || 'text',
    versions: [],
    currentVersionIndex: userMessage.currentVersionIndex || 0,
    metadata: {
      migratedFrom: 'legacy',
      migrationTimestamp: Date.now()
    }
  };

  // 迁移版本数据
  if (userMessage.versions && Array.isArray(userMessage.versions)) {
    userMessage.versions.forEach((oldVersion, versionIndex) => {
      const newVersion = migrateMessageVersion(oldVersion, userMessage, allMessages, messageIndex, migrationResult);
      newMessage.versions.push(newVersion);
      migrationResult.statistics.migratedVersions++;
    });
  } else {
    // 如果没有版本数据，创建默认版本
    const defaultVersion = createMessageVersion(userMessage.content || '');
    
    // 查找对应的AI回复
    const aiReply = findCorrespondingAIReply(userMessage, allMessages, messageIndex);
    if (aiReply) {
      defaultVersion.aiReply = createAIReply(aiReply.content, AIReplyStatus.COMPLETED);
      migrationResult.statistics.aiReplies++;
    }
    
    newMessage.versions.push(defaultVersion);
    migrationResult.statistics.migratedVersions++;
  }

  // 确保currentVersionIndex有效
  if (newMessage.currentVersionIndex >= newMessage.versions.length) {
    newMessage.currentVersionIndex = newMessage.versions.length - 1;
    migrationResult.warnings.push(`消息 ${newMessage.id} 的版本索引已调整`);
  }

  return newMessage;
}

/**
 * 迁移消息版本
 * @param {Object} oldVersion - 旧版本数据
 * @param {Object} userMessage - 用户消息
 * @param {Array} allMessages - 所有消息数组
 * @param {number} messageIndex - 消息索引
 * @param {Object} migrationResult - 迁移结果对象
 * @returns {Object} 新版本数据
 */
function migrateMessageVersion(oldVersion, userMessage, allMessages, messageIndex, migrationResult) {
  const newVersion = {
    id: oldVersion.id || `ver_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    content: oldVersion.content || '',
    timestamp: oldVersion.timestamp || userMessage.timestamp,
    aiReply: null,
    metadata: {
      migratedFrom: 'legacy',
      migrationTimestamp: Date.now(),
      editReason: oldVersion.metadata?.editReason || null,
      isAutoSaved: oldVersion.metadata?.isAutoSaved || false,
      editType: oldVersion.metadata?.editType || 'manual',
      source: 'migration'
    }
  };

  // 处理版本中的AI回复
  if (oldVersion.aiReply) {
    if (typeof oldVersion.aiReply === 'string') {
      // 旧格式：AI回复是字符串
      newVersion.aiReply = createAIReply(oldVersion.aiReply, AIReplyStatus.COMPLETED, {
        migrationSource: 'legacy_string',
        migrationTimestamp: Date.now()
      });
      migrationResult.statistics.aiReplies++;
    } else if (typeof oldVersion.aiReply === 'object') {
      // 新格式：AI回复已经是对象，但需要补充新字段
      const oldAIReply = oldVersion.aiReply;
      newVersion.aiReply = createAIReply(
        oldAIReply.content || '',
        oldAIReply.status || AIReplyStatus.COMPLETED,
        {
          startTimestamp: oldAIReply.timestamp || Date.now(),
          endTimestamp: oldAIReply.timestamp || Date.now(),
          model: oldAIReply.metadata?.model || null,
          tokens: oldAIReply.metadata?.tokens || 0,
          error: oldAIReply.metadata?.error || null,
          migrationSource: 'legacy_object',
          migrationTimestamp: Date.now(),
          metadata: oldAIReply.metadata || {}
        }
      );
      // 保持原有ID如果存在
      if (oldAIReply.id) {
        newVersion.aiReply.id = oldAIReply.id;
      }
      migrationResult.statistics.aiReplies++;
    }
  } else {
    // 如果版本中没有AI回复，尝试查找对应的独立AI消息
    const aiReply = findCorrespondingAIReply(userMessage, allMessages, messageIndex);
    if (aiReply) {
      newVersion.aiReply = createAIReply(aiReply.content, AIReplyStatus.COMPLETED, {
        migrationSource: 'independent_message',
        migrationTimestamp: Date.now(),
        originalMessageId: aiReply.id
      });
      migrationResult.statistics.aiReplies++;
    }
  }

  return newVersion;
}

/**
 * 查找对应的AI回复消息
 * @param {Object} userMessage - 用户消息
 * @param {Array} allMessages - 所有消息数组
 * @param {number} userMessageIndex - 用户消息索引
 * @returns {Object|null} AI回复消息或null
 */
function findCorrespondingAIReply(userMessage, allMessages, userMessageIndex) {
  // 查找用户消息后面的第一个AI消息
  for (let i = userMessageIndex + 1; i < allMessages.length; i++) {
    const message = allMessages[i];
    
    if (!message.isUser) {
      // 找到AI消息，检查是否属于当前用户消息
      return message;
    } else {
      // 遇到下一个用户消息，停止查找
      break;
    }
  }
  
  return null;
}

/**
 * 检查AI消息是否与用户消息关联
 * @param {Object} aiMessage - AI消息
 * @param {Array} allMessages - 所有消息数组
 * @param {number} aiMessageIndex - AI消息索引
 * @returns {boolean} 是否关联
 */
function isAIMessageLinkedToUser(aiMessage, allMessages, aiMessageIndex) {
  // 向前查找最近的用户消息
  for (let i = aiMessageIndex - 1; i >= 0; i--) {
    const message = allMessages[i];
    
    if (message.isUser) {
      return true; // 找到用户消息，说明AI消息有关联
    }
  }
  
  return false; // 没有找到用户消息，说明是孤立的AI消息
}

/**
 * 验证迁移后的数据
 * @param {Array} newMessages - 新格式的消息数组
 * @returns {Object} 验证结果
 */
function validateMigratedData(newMessages) {
  const warnings = [];
  const errors = [];
  const statistics = {
    totalMessages: newMessages.length,
    validMessages: 0,
    invalidMessages: 0,
    totalVersions: 0,
    totalAIReplies: 0,
    activeAIReplies: 0,
    completedAIReplies: 0,
    errorAIReplies: 0
  };

  newMessages.forEach((message, index) => {
    const validation = validateMessage(message);

    if (validation.isValid) {
      statistics.validMessages++;
    } else {
      statistics.invalidMessages++;
      errors.push(`消息 ${index} 验证失败: ${validation.errors.join(', ')}`);
    }

    if (validation.warnings.length > 0) {
      warnings.push(`消息 ${index} 警告: ${validation.warnings.join(', ')}`);
    }

    // 统计版本和AI回复信息
    if (message.versions) {
      statistics.totalVersions += message.versions.length;

      message.versions.forEach(version => {
        if (version.aiReply) {
          statistics.totalAIReplies++;

          switch (version.aiReply.status) {
            case 'generating':
            case 'pending':
              statistics.activeAIReplies++;
              break;
            case 'completed':
              statistics.completedAIReplies++;
              break;
            case 'error':
            case 'cancelled':
            case 'timeout':
              statistics.errorAIReplies++;
              break;
          }
        }
      });
    }

    // 检查数据一致性
    if (message.currentVersionIndex >= message.versions.length) {
      errors.push(`消息${index}的当前版本索引超出范围`);
    }

    // 检查是否只包含用户消息
    if (!message.isUser) {
      errors.push(`消息${index}不是用户消息，违反要求`);
    }
  });

  return { warnings, errors, statistics };
}

/**
 * 创建数据备份
 * @param {Array} data - 要备份的数据
 * @param {Object} options - 备份选项
 * @returns {Object} 备份信息
 */
function createBackup(data, options = {}) {
  const timestamp = Date.now();
  const backup = {
    timestamp,
    version: options.version || 'legacy',
    dataVersion: options.dataVersion || '1.0',
    migrationId: options.migrationId || `migration_${timestamp}`,
    data: JSON.parse(JSON.stringify(data)), // 深拷贝
    metadata: {
      messageCount: data.length,
      userMessageCount: data.filter(m => m.isUser).length,
      aiMessageCount: data.filter(m => !m.isUser).length,
      backupReason: options.reason || 'migration',
      browserInfo: navigator.userAgent,
      ...options.metadata
    }
  };

  // 保存到localStorage（主备份）
  const backupKey = options.key || 'message-data-backup';
  try {
    localStorage.setItem(backupKey, JSON.stringify(backup));

    // 创建额外的时间戳备份
    const timestampKey = `${backupKey}_${timestamp}`;
    localStorage.setItem(timestampKey, JSON.stringify(backup));

    // 维护备份列表
    updateBackupList(timestampKey, backup);

    console.log(`数据备份已创建: ${backupKey}, 时间戳: ${new Date(timestamp).toISOString()}`);
    return {
      success: true,
      timestamp: backup.timestamp,
      backupKey,
      timestampKey,
      messageCount: backup.metadata.messageCount
    };
  } catch (error) {
    console.error('创建备份失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 维护备份列表
 * @param {string} backupKey - 备份键
 * @param {Object} backup - 备份数据
 */
function updateBackupList(backupKey, backup) {
  try {
    const backupListKey = 'message-backup-list';
    const existingList = JSON.parse(localStorage.getItem(backupListKey) || '[]');

    existingList.push({
      key: backupKey,
      timestamp: backup.timestamp,
      version: backup.version,
      messageCount: backup.metadata.messageCount,
      reason: backup.metadata.backupReason
    });

    // 只保留最近的10个备份记录
    const sortedList = existingList
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);

    localStorage.setItem(backupListKey, JSON.stringify(sortedList));

    // 清理旧的备份数据
    existingList.slice(10).forEach(oldBackup => {
      try {
        localStorage.removeItem(oldBackup.key);
      } catch (e) {
        console.warn('清理旧备份失败:', e);
      }
    });
  } catch (error) {
    console.warn('维护备份列表失败:', error);
  }
}

/**
 * 恢复数据备份
 * @param {Object} options - 恢复选项
 * @returns {Object} 恢复结果
 */
export function restoreFromBackup(options = {}) {
  const result = {
    success: false,
    data: null,
    backup: null,
    error: null,
    statistics: null
  };

  try {
    const backupKey = options.backupKey || 'message-data-backup';
    const backupStr = localStorage.getItem(backupKey);

    if (!backupStr) {
      result.error = '未找到备份数据';
      return result;
    }

    const backup = JSON.parse(backupStr);

    // 验证备份数据
    if (!backup.data || !Array.isArray(backup.data)) {
      result.error = '备份数据格式无效';
      return result;
    }

    // 验证备份完整性
    const expectedCount = backup.metadata?.messageCount || 0;
    if (backup.data.length !== expectedCount) {
      console.warn(`备份数据数量不匹配: 期望 ${expectedCount}, 实际 ${backup.data.length}`);
    }

    result.success = true;
    result.data = backup.data;
    result.backup = backup;
    result.statistics = {
      messageCount: backup.data.length,
      backupTimestamp: backup.timestamp,
      backupVersion: backup.version,
      backupAge: Date.now() - backup.timestamp
    };

    console.log('恢复数据备份成功:', {
      timestamp: new Date(backup.timestamp).toISOString(),
      messageCount: backup.data.length,
      version: backup.version
    });

    return result;
  } catch (error) {
    console.error('恢复备份失败:', error);
    result.error = error.message;
    return result;
  }
}

/**
 * 获取可用的备份列表
 * @returns {Array} 备份列表
 */
export function getAvailableBackups() {
  try {
    const backupListKey = 'message-backup-list';
    const backupList = JSON.parse(localStorage.getItem(backupListKey) || '[]');

    return backupList.map(backup => ({
      ...backup,
      age: Date.now() - backup.timestamp,
      formattedDate: new Date(backup.timestamp).toLocaleString()
    }));
  } catch (error) {
    console.error('获取备份列表失败:', error);
    return [];
  }
}

/**
 * 删除指定的备份
 * @param {string} backupKey - 备份键
 * @returns {boolean} 是否成功删除
 */
export function deleteBackup(backupKey) {
  try {
    localStorage.removeItem(backupKey);

    // 从备份列表中移除
    const backupListKey = 'message-backup-list';
    const backupList = JSON.parse(localStorage.getItem(backupListKey) || '[]');
    const updatedList = backupList.filter(backup => backup.key !== backupKey);
    localStorage.setItem(backupListKey, JSON.stringify(updatedList));

    console.log('备份已删除:', backupKey);
    return true;
  } catch (error) {
    console.error('删除备份失败:', error);
    return false;
  }
}

/**
 * 清理备份数据
 */
export function clearBackup() {
  try {
    localStorage.removeItem('message-data-backup');
    console.log('备份数据已清理');
  } catch (error) {
    console.error('清理备份失败:', error);
  }
}
