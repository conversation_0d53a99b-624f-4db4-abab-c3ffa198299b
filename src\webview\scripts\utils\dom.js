/**
 * DOM工具函数
 * 提供常用的DOM操作和查询功能
 */

/**
 * DOM工具类
 */
export class DOMUtils {
  /**
   * 查询单个元素
   * @param {string} selector - CSS选择器
   * @param {Element} context - 查询上下文，默认为document
   * @returns {Element|null} 元素或null
   */
  static query(selector, context = document) {
    return context.querySelector(selector);
  }

  /**
   * 查询多个元素
   * @param {string} selector - CSS选择器
   * @param {Element} context - 查询上下文，默认为document
   * @returns {NodeList} 元素列表
   */
  static queryAll(selector, context = document) {
    return context.querySelectorAll(selector);
  }

  /**
   * 创建元素
   * @param {string} tagName - 标签名
   * @param {object} attributes - 属性对象
   * @param {string|Element|Array} children - 子元素
   * @returns {Element} 创建的元素
   */
  static createElement(tagName, attributes = {}, children = null) {
    const element = document.createElement(tagName);

    // 设置属性
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key === 'innerHTML') {
        element.innerHTML = value;
      } else if (key === 'textContent') {
        element.textContent = value;
      } else if (key === 'value') {
        // 特殊处理 value 属性，确保对 input/textarea 正确设置
        element.value = value;
      } else if (key.startsWith('data-')) {
        element.setAttribute(key, value);
      } else if (key.startsWith('on') && typeof value === 'function') {
        element.addEventListener(key.slice(2).toLowerCase(), value);
      } else {
        element.setAttribute(key, value);
      }
    });

    // 添加子元素
    if (children) {
      this.appendChildren(element, children);
    }

    return element;
  }

  /**
   * 添加子元素
   * @param {Element} parent - 父元素
   * @param {string|Element|Array} children - 子元素
   */
  static appendChildren(parent, children) {
    if (typeof children === 'string') {
      parent.textContent = children;
    } else if (children instanceof Element) {
      parent.appendChild(children);
    } else if (Array.isArray(children)) {
      children.forEach(child => this.appendChildren(parent, child));
    }
  }

  /**
   * 添加CSS类
   * @param {Element} element - 元素
   * @param {...string} classNames - 类名
   */
  static addClass(element, ...classNames) {
    element.classList.add(...classNames);
  }

  /**
   * 移除CSS类
   * @param {Element} element - 元素
   * @param {...string} classNames - 类名
   */
  static removeClass(element, ...classNames) {
    element.classList.remove(...classNames);
  }

  /**
   * 切换CSS类
   * @param {Element} element - 元素
   * @param {string} className - 类名
   * @param {boolean} force - 强制添加或移除
   * @returns {boolean} 是否包含该类
   */
  static toggleClass(element, className, force) {
    return element.classList.toggle(className, force);
  }

  /**
   * 检查是否包含CSS类
   * @param {Element} element - 元素
   * @param {string} className - 类名
   * @returns {boolean} 是否包含
   */
  static hasClass(element, className) {
    return element.classList.contains(className);
  }

  /**
   * 设置样式
   * @param {Element} element - 元素
   * @param {object|string} styles - 样式对象或样式字符串
   * @param {string} value - 样式值（当styles为字符串时）
   */
  static setStyle(element, styles, value) {
    if (typeof styles === 'string') {
      element.style[styles] = value;
    } else {
      Object.entries(styles).forEach(([property, val]) => {
        element.style[property] = val;
      });
    }
  }

  /**
   * 获取样式
   * @param {Element} element - 元素
   * @param {string} property - 样式属性
   * @returns {string} 样式值
   */
  static getStyle(element, property) {
    return window.getComputedStyle(element)[property];
  }

  /**
   * 显示元素
   * @param {Element} element - 元素
   * @param {string} display - 显示类型，默认为'block'
   */
  static show(element, display = 'block') {
    element.style.display = display;
  }

  /**
   * 隐藏元素
   * @param {Element} element - 元素
   */
  static hide(element) {
    element.style.display = 'none';
  }

  /**
   * 切换显示/隐藏
   * @param {Element} element - 元素
   * @param {string} display - 显示类型
   * @returns {boolean} 是否可见
   */
  static toggle(element, display = 'block') {
    const isVisible = element.style.display !== 'none';
    element.style.display = isVisible ? 'none' : display;
    return !isVisible;
  }

  /**
   * 移除元素
   * @param {Element} element - 元素
   */
  static remove(element) {
    if (element && element.parentNode) {
      element.parentNode.removeChild(element);
    }
  }

  /**
   * 清空元素内容
   * @param {Element} element - 元素
   */
  static empty(element) {
    while (element.firstChild) {
      element.removeChild(element.firstChild);
    }
  }

  /**
   * 获取元素位置
   * @param {Element} element - 元素
   * @returns {object} 位置信息
   */
  static getPosition(element) {
    const rect = element.getBoundingClientRect();
    return {
      top: rect.top + window.pageYOffset,
      left: rect.left + window.pageXOffset,
      width: rect.width,
      height: rect.height
    };
  }

  /**
   * 检查元素是否在视口中
   * @param {Element} element - 元素
   * @returns {boolean} 是否在视口中
   */
  static isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  /**
   * 滚动到元素
   * @param {Element} element - 元素
   * @param {object} options - 滚动选项
   */
  static scrollToElement(element, options = {}) {
    const defaultOptions = {
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest'
    };

    element.scrollIntoView({ ...defaultOptions, ...options });
  }

  /**
   * 防抖函数
   * @param {function} func - 要防抖的函数
   * @param {number} wait - 等待时间
   * @param {boolean} immediate - 是否立即执行
   * @returns {function} 防抖后的函数
   */
  static debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) { func(...args); }
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) { func(...args); }
    };
  }

  /**
   * 节流函数
   * @param {function} func - 要节流的函数
   * @param {number} limit - 限制时间
   * @returns {function} 节流后的函数
   */
  static throttle(func, limit) {
    let inThrottle;
    return function (...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * 等待DOM加载完成
   * @returns {Promise} Promise对象
   */
  static ready() {
    return new Promise(resolve => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve);
      } else {
        resolve();
      }
    });
  }

  /**
   * 动画帧请求
   * @param {function} callback - 回调函数
   * @returns {number} 请求ID
   */
  static requestAnimationFrame(callback) {
    return window.requestAnimationFrame(callback);
  }

  /**
   * 取消动画帧请求
   * @param {number} id - 请求ID
   */
  static cancelAnimationFrame(id) {
    window.cancelAnimationFrame(id);
  }

  /**
   * 复制文本到剪贴板
   * @param {string} text - 要复制的文本
   * @returns {Promise} Promise对象
   */
  static async copyToClipboard(text) {
    if (navigator.clipboard) {
      return navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return Promise.resolve();
    }
  }
}
