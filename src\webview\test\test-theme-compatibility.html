<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VS Code主题兼容性测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .theme-info {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            margin-bottom: var(--spacing-lg);
        }
        
        .theme-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .theme-preview {
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            overflow: hidden;
            transition: all var(--transition-fast);
        }
        
        .theme-preview:hover {
            border-color: var(--color-primary);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .theme-header {
            background: var(--color-surface-hover);
            padding: var(--spacing-md);
            font-weight: var(--font-weight-bold);
            text-align: center;
        }
        
        .theme-content {
            padding: var(--spacing-md);
            min-height: 200px;
        }
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .color-swatch {
            height: 40px;
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-bold);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
        
        .component-showcase {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .compatibility-matrix {
            display: grid;
            grid-template-columns: 1fr repeat(4, auto);
            gap: var(--spacing-xs);
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }
        
        .compatibility-header {
            font-weight: var(--font-weight-bold);
            padding: var(--spacing-sm);
            background: var(--color-surface-hover);
            text-align: center;
        }
        
        .compatibility-cell {
            padding: var(--spacing-sm);
            text-align: center;
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
        }
        
        .compatibility-pass {
            background: var(--color-success);
            color: white;
        }
        
        .compatibility-fail {
            background: var(--color-error);
            color: white;
        }
        
        .compatibility-partial {
            background: var(--color-warning);
            color: black;
        }
        
        .contrast-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .contrast-sample {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            text-align: center;
            position: relative;
        }
        
        .contrast-ratio {
            position: absolute;
            top: var(--spacing-xs);
            right: var(--spacing-xs);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
        }
        
        .accessibility-indicators {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
            justify-content: center;
        }
        
        .accessibility-badge {
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-bold);
        }
        
        .badge-aa {
            background: var(--color-success);
            color: white;
        }
        
        .badge-aaa {
            background: var(--color-info);
            color: white;
        }
        
        .badge-fail {
            background: var(--color-error);
            color: white;
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .theme-simulator {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>VS Code主题兼容性测试</h1>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="testAllThemes()">测试所有主题</button>
                <button class="btn btn-secondary" onclick="testContrastRatios()">测试对比度</button>
                <button class="btn btn-secondary" onclick="simulateVSCodeThemes()">模拟VS Code主题</button>
                <button class="btn btn-secondary" onclick="testAccessibility()">无障碍测试</button>
                <button class="btn btn-ghost" onclick="exportCompatibilityReport()">导出兼容性报告</button>
            </div>
        </div>
        
        <!-- 当前主题信息 -->
        <div class="test-section">
            <h2>当前主题信息</h2>
            <div class="theme-info" id="themeInfo">
                正在检测主题信息...
            </div>
        </div>
        
        <!-- 主题预览 -->
        <div class="test-section">
            <h2>主题预览</h2>
            <div class="theme-grid">
                <div class="theme-preview" data-theme="light">
                    <div class="theme-header">浅色主题</div>
                    <div class="theme-content">
                        <div class="color-palette">
                            <div class="color-swatch" style="background: #0078d4; color: white;">主色</div>
                            <div class="color-swatch" style="background: #107c10; color: white;">成功</div>
                            <div class="color-swatch" style="background: #ff8c00; color: white;">警告</div>
                            <div class="color-swatch" style="background: #d13438; color: white;">错误</div>
                        </div>
                        <div class="component-showcase">
                            <button class="btn btn-primary">主要按钮</button>
                            <button class="btn btn-secondary">次要按钮</button>
                            <input type="text" class="input" placeholder="输入框">
                            <div class="demo-message demo-message-user">
                                <div class="demo-message-content">用户消息</div>
                            </div>
                            <div class="demo-message demo-message-ai">
                                <div class="demo-message-content">AI消息</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="theme-preview" data-theme="dark">
                    <div class="theme-header">深色主题</div>
                    <div class="theme-content">
                        <div class="color-palette">
                            <div class="color-swatch" style="background: #0078d4; color: white;">主色</div>
                            <div class="color-swatch" style="background: #89d185; color: black;">成功</div>
                            <div class="color-swatch" style="background: #ffcc02; color: black;">警告</div>
                            <div class="color-swatch" style="background: #f85149; color: white;">错误</div>
                        </div>
                        <div class="component-showcase">
                            <button class="btn btn-primary">主要按钮</button>
                            <button class="btn btn-secondary">次要按钮</button>
                            <input type="text" class="input" placeholder="输入框">
                            <div class="demo-message demo-message-user">
                                <div class="demo-message-content">用户消息</div>
                            </div>
                            <div class="demo-message demo-message-ai">
                                <div class="demo-message-content">AI消息</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="theme-preview" data-theme="high-contrast">
                    <div class="theme-header">高对比度主题</div>
                    <div class="theme-content">
                        <div class="color-palette">
                            <div class="color-swatch" style="background: #ffffff; color: black;">主色</div>
                            <div class="color-swatch" style="background: #00ff00; color: black;">成功</div>
                            <div class="color-swatch" style="background: #ffff00; color: black;">警告</div>
                            <div class="color-swatch" style="background: #ff0000; color: white;">错误</div>
                        </div>
                        <div class="component-showcase">
                            <button class="btn btn-primary">主要按钮</button>
                            <button class="btn btn-secondary">次要按钮</button>
                            <input type="text" class="input" placeholder="输入框">
                            <div class="demo-message demo-message-user">
                                <div class="demo-message-content">用户消息</div>
                            </div>
                            <div class="demo-message demo-message-ai">
                                <div class="demo-message-content">AI消息</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 兼容性矩阵 -->
        <div class="test-section">
            <h2>兼容性矩阵</h2>
            <div class="compatibility-matrix" id="compatibilityMatrix">
                <!-- 兼容性数据将在这里显示 -->
            </div>
        </div>
        
        <!-- 对比度测试 -->
        <div class="test-section">
            <h2>颜色对比度测试</h2>
            <div class="contrast-test" id="contrastTest">
                <!-- 对比度测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- VS Code主题模拟器 -->
        <div class="test-section">
            <h2>VS Code主题模拟器</h2>
            <div class="control-panel">
                <button class="btn btn-secondary" onclick="simulateTheme('One Dark Pro')">One Dark Pro</button>
                <button class="btn btn-secondary" onclick="simulateTheme('Dracula')">Dracula</button>
                <button class="btn btn-secondary" onclick="simulateTheme('Material Theme')">Material Theme</button>
                <button class="btn btn-secondary" onclick="simulateTheme('Monokai')">Monokai</button>
                <button class="btn btn-secondary" onclick="simulateTheme('Solarized')">Solarized</button>
                <button class="btn btn-ghost" onclick="resetTheme()">重置</button>
            </div>
            <div class="theme-simulator" id="themeSimulator">
                <p>选择一个主题来查看模拟效果</p>
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { globalThemeManager } from './scripts/utils/theme.js';
        import { ToastComponent } from './scripts/components/toast.js';
        
        // 全局变量
        window.themeManager = globalThemeManager;
        window.toastComponent = new ToastComponent();
        window.compatibilityData = {};
        
        // 初始化
        Promise.all([
            window.toastComponent.init()
        ]).then(() => {
            console.log('主题兼容性测试页面初始化完成');
            updateThemeInfo();
            setupThemeObserver();
            generateCompatibilityMatrix();
            testContrastRatios();
        }).catch(error => {
            console.error('初始化失败:', error);
        });
        
        // 设置主题观察者
        function setupThemeObserver() {
            globalThemeManager.addObserver((themeInfo) => {
                updateThemeInfo();
                console.log('主题变化:', themeInfo);
            });
        }
        
        // 更新主题信息
        function updateThemeInfo() {
            const themeInfo = document.getElementById('themeInfo');
            const current = globalThemeManager.getTheme();
            const vscode = globalThemeManager.getVSCodeTheme();
            const compatibility = globalThemeManager.checkThemeCompatibility();
            
            themeInfo.innerHTML = `
当前主题: ${current}
系统主题: ${globalThemeManager.systemTheme}

VS Code主题信息:
- 类型: ${vscode.kind || '未检测到'}
- 名称: ${vscode.name || '未知'}
- 是否深色: ${vscode.isDark ? '是' : '否'}
- 是否高对比度: ${vscode.isHighContrast ? '是' : '否'}

兼容性检查:
- VS Code集成: ${compatibility.vsCodeIntegration ? '✓' : '✗'}
- 系统主题支持: ${compatibility.systemThemeSupport ? '✓' : '✗'}
- 高对比度支持: ${compatibility.highContrastSupport ? '✓' : '✗'}
- 减少动画支持: ${compatibility.reducedMotionSupport ? '✓' : '✗'}
- 强制颜色支持: ${compatibility.forcedColorsSupport ? '✓' : '✗'}
- 总体评分: ${(compatibility.overallScore * 100).toFixed(1)}%
            `.trim();
        }
        
        // 生成兼容性矩阵
        function generateCompatibilityMatrix() {
            const matrix = document.getElementById('compatibilityMatrix');
            const themes = ['light', 'dark', 'high-contrast', 'auto'];
            const features = ['按钮', '输入框', '消息', '通知'];
            
            // 清空矩阵
            matrix.innerHTML = '';
            
            // 添加标题行
            matrix.appendChild(createCell('功能/主题', 'compatibility-header'));
            themes.forEach(theme => {
                matrix.appendChild(createCell(theme, 'compatibility-header'));
            });
            
            // 添加数据行
            features.forEach(feature => {
                matrix.appendChild(createCell(feature, 'compatibility-cell'));
                themes.forEach(theme => {
                    const compatibility = testFeatureCompatibility(feature, theme);
                    const cell = createCell(compatibility.status, `compatibility-cell compatibility-${compatibility.level}`);
                    matrix.appendChild(cell);
                });
            });
        }
        
        function createCell(content, className) {
            const cell = document.createElement('div');
            cell.className = className;
            cell.textContent = content;
            return cell;
        }
        
        function testFeatureCompatibility(feature, theme) {
            // 模拟兼容性测试
            const random = Math.random();
            if (random > 0.8) {
                return { status: '✗', level: 'fail' };
            } else if (random > 0.6) {
                return { status: '△', level: 'partial' };
            } else {
                return { status: '✓', level: 'pass' };
            }
        }
        
        // 测试对比度
        window.testContrastRatios = function() {
            const contrastTest = document.getElementById('contrastTest');
            contrastTest.innerHTML = '';
            
            const colorPairs = [
                { fg: '#000000', bg: '#ffffff', name: '黑白' },
                { fg: '#ffffff', bg: '#0078d4', name: '白蓝' },
                { fg: '#323130', bg: '#f8f8f8', name: '深灰浅灰' },
                { fg: '#cccccc', bg: '#1e1e1e', name: '浅灰深色' },
                { fg: '#ffffff', bg: '#d13438', name: '白红' },
                { fg: '#000000', bg: '#ffff00', name: '黑黄' }
            ];
            
            colorPairs.forEach(pair => {
                const contrast = calculateContrastRatio(pair.fg, pair.bg);
                const sample = document.createElement('div');
                sample.className = 'contrast-sample';
                sample.style.color = pair.fg;
                sample.style.background = pair.bg;
                
                sample.innerHTML = `
                    <div class="contrast-ratio">${contrast.toFixed(2)}:1</div>
                    <h4>${pair.name}</h4>
                    <p>这是一段测试文本，用于检查颜色对比度是否符合无障碍标准。</p>
                    <div class="accessibility-indicators">
                        <span class="accessibility-badge ${contrast >= 4.5 ? 'badge-aa' : 'badge-fail'}">
                            AA ${contrast >= 4.5 ? '✓' : '✗'}
                        </span>
                        <span class="accessibility-badge ${contrast >= 7 ? 'badge-aaa' : 'badge-fail'}">
                            AAA ${contrast >= 7 ? '✓' : '✗'}
                        </span>
                    </div>
                `;
                
                contrastTest.appendChild(sample);
            });
        };
        
        // 计算对比度比率
        function calculateContrastRatio(color1, color2) {
            const lum1 = getLuminance(color1);
            const lum2 = getLuminance(color2);
            const brightest = Math.max(lum1, lum2);
            const darkest = Math.min(lum1, lum2);
            return (brightest + 0.05) / (darkest + 0.05);
        }
        
        function getLuminance(color) {
            const rgb = hexToRgb(color);
            const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
                c = c / 255;
                return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
            });
            return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        }
        
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }
        
        // 模拟VS Code主题
        window.simulateTheme = function(themeName) {
            const simulator = document.getElementById('themeSimulator');
            const themeColors = getThemeColors(themeName);
            
            simulator.innerHTML = `
                <h3>${themeName} 主题模拟</h3>
                <div class="color-palette">
                    <div class="color-swatch" style="background: ${themeColors.primary}; color: white;">主色</div>
                    <div class="color-swatch" style="background: ${themeColors.success}; color: white;">成功</div>
                    <div class="color-swatch" style="background: ${themeColors.warning}; color: black;">警告</div>
                    <div class="color-swatch" style="background: ${themeColors.error}; color: white;">错误</div>
                </div>
                <div class="component-showcase">
                    <button class="btn btn-primary" style="background: ${themeColors.primary};">主要按钮</button>
                    <button class="btn btn-secondary">次要按钮</button>
                    <input type="text" class="input" placeholder="输入框">
                </div>
            `;
            
            // 临时应用主题颜色
            document.documentElement.style.setProperty('--color-primary', themeColors.primary);
            document.documentElement.style.setProperty('--color-success', themeColors.success);
            document.documentElement.style.setProperty('--color-warning', themeColors.warning);
            document.documentElement.style.setProperty('--color-error', themeColors.error);
        };
        
        function getThemeColors(themeName) {
            const themes = {
                'One Dark Pro': {
                    primary: '#61afef',
                    success: '#98c379',
                    warning: '#e5c07b',
                    error: '#e06c75'
                },
                'Dracula': {
                    primary: '#bd93f9',
                    success: '#50fa7b',
                    warning: '#f1fa8c',
                    error: '#ff5555'
                },
                'Material Theme': {
                    primary: '#82aaff',
                    success: '#c3e88d',
                    warning: '#ffcb6b',
                    error: '#f07178'
                },
                'Monokai': {
                    primary: '#66d9ef',
                    success: '#a6e22e',
                    warning: '#e6db74',
                    error: '#f92672'
                },
                'Solarized': {
                    primary: '#268bd2',
                    success: '#859900',
                    warning: '#b58900',
                    error: '#dc322f'
                }
            };
            
            return themes[themeName] || themes['One Dark Pro'];
        }
        
        window.resetTheme = function() {
            document.documentElement.style.removeProperty('--color-primary');
            document.documentElement.style.removeProperty('--color-success');
            document.documentElement.style.removeProperty('--color-warning');
            document.documentElement.style.removeProperty('--color-error');
            
            document.getElementById('themeSimulator').innerHTML = '<p>选择一个主题来查看模拟效果</p>';
        };
        
        window.testAllThemes = function() {
            const themes = ['light', 'dark', 'high-contrast'];
            let index = 0;
            
            function testNext() {
                if (index < themes.length) {
                    globalThemeManager.setTheme(themes[index]);
                    window.toastComponent.show({
                        type: 'info',
                        message: `正在测试 ${themes[index]} 主题`
                    });
                    index++;
                    setTimeout(testNext, 2000);
                } else {
                    globalThemeManager.setTheme('auto');
                    window.toastComponent.show({
                        type: 'success',
                        message: '所有主题测试完成'
                    });
                }
            }
            
            testNext();
        };
        
        window.testAccessibility = function() {
            const compatibility = globalThemeManager.checkThemeCompatibility();
            const score = compatibility.overallScore * 100;
            
            let message = `无障碍评分: ${score.toFixed(1)}%`;
            let type = 'info';
            
            if (score >= 80) {
                type = 'success';
                message += ' - 优秀';
            } else if (score >= 60) {
                type = 'warning';
                message += ' - 良好';
            } else {
                type = 'error';
                message += ' - 需要改进';
            }
            
            window.toastComponent.show({ type, message });
        };
        
        window.exportCompatibilityReport = function() {
            const report = {
                timestamp: new Date().toISOString(),
                theme: globalThemeManager.getTheme(),
                vscodeTheme: globalThemeManager.getVSCodeTheme(),
                compatibility: globalThemeManager.checkThemeCompatibility(),
                userAgent: navigator.userAgent
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `theme-compatibility-report-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            window.toastComponent.show({
                type: 'success',
                message: '兼容性报告已导出'
            });
        };
        
        console.log('主题兼容性测试页面已加载');
    </script>
</body>
</html>
