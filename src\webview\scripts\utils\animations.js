/**
 * 动画工具类
 * 提供各种动画效果和微交互功能
 */

/**
 * 动画类型枚举
 */
export const AnimationTypes = {
  FADE_IN: 'fadeIn',
  FADE_OUT: 'fadeOut',
  SLIDE_IN_LEFT: 'slideInLeft',
  SLIDE_IN_RIGHT: 'slideInRight',
  SLIDE_IN_UP: 'slideInUp',
  SLIDE_IN_DOWN: 'slideInDown',
  SLIDE_OUT_LEFT: 'slideOutLeft',
  SLIDE_OUT_RIGHT: 'slideOutRight',
  SLIDE_OUT_UP: 'slideOutUp',
  SLIDE_OUT_DOWN: 'slideOutDown',
  SCALE_IN: 'scaleIn',
  SCALE_OUT: 'scaleOut',
  BOUNCE_IN: 'bounceIn',
  SHAKE: 'shake',
  PULSE: 'pulse',
  FLIP: 'flip'
};

/**
 * 动画持续时间枚举
 */
export const AnimationDurations = {
  FAST: 150,
  NORMAL: 250,
  SLOW: 350,
  VERY_SLOW: 500
};

/**
 * 缓动函数枚举
 */
export const EasingFunctions = {
  EASE: 'ease',
  EASE_IN: 'ease-in',
  EASE_OUT: 'ease-out',
  EASE_IN_OUT: 'ease-in-out',
  LINEAR: 'linear',
  BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  SMOOTH: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
};

/**
 * 动画管理器类
 */
export class AnimationManager {
  constructor() {
    this.activeAnimations = new Map();
    this.animationId = 0;
  }

  /**
   * 执行动画
   * @param {Element} element - 目标元素
   * @param {string} type - 动画类型
   * @param {object} options - 动画选项
   * @returns {Promise} 动画完成的Promise
   */
  animate(element, type, options = {}) {
    const {
      duration = AnimationDurations.NORMAL,
      easing = EasingFunctions.EASE_IN_OUT,
      delay = 0,
      fillMode = 'both',
      onStart = null,
      onComplete = null
    } = options;

    return new Promise((resolve, reject) => {
      try {
        // 生成动画ID
        const animationId = ++this.animationId;
        
        // 获取动画关键帧
        const keyframes = this.getKeyframes(type);
        
        if (!keyframes) {
          reject(new Error(`Unknown animation type: ${type}`));
          return;
        }

        // 创建动画选项
        const animationOptions = {
          duration,
          easing,
          delay,
          fill: fillMode
        };

        // 执行动画前回调
        if (onStart) {
          onStart(element);
        }

        // 创建Web Animations API动画
        const animation = element.animate(keyframes, animationOptions);
        
        // 存储动画引用
        this.activeAnimations.set(animationId, animation);

        // 监听动画完成
        animation.addEventListener('finish', () => {
          this.activeAnimations.delete(animationId);
          if (onComplete) {
            onComplete(element);
          }
          resolve(animation);
        });

        // 监听动画取消
        animation.addEventListener('cancel', () => {
          this.activeAnimations.delete(animationId);
          reject(new Error('Animation was cancelled'));
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 获取动画关键帧
   * @param {string} type - 动画类型
   * @returns {Array} 关键帧数组
   */
  getKeyframes(type) {
    const keyframes = {
      [AnimationTypes.FADE_IN]: [
        { opacity: 0 },
        { opacity: 1 }
      ],
      [AnimationTypes.FADE_OUT]: [
        { opacity: 1 },
        { opacity: 0 }
      ],
      [AnimationTypes.SLIDE_IN_LEFT]: [
        { transform: 'translateX(-100%)', opacity: 0 },
        { transform: 'translateX(0)', opacity: 1 }
      ],
      [AnimationTypes.SLIDE_IN_RIGHT]: [
        { transform: 'translateX(100%)', opacity: 0 },
        { transform: 'translateX(0)', opacity: 1 }
      ],
      [AnimationTypes.SLIDE_IN_UP]: [
        { transform: 'translateY(100%)', opacity: 0 },
        { transform: 'translateY(0)', opacity: 1 }
      ],
      [AnimationTypes.SLIDE_IN_DOWN]: [
        { transform: 'translateY(-100%)', opacity: 0 },
        { transform: 'translateY(0)', opacity: 1 }
      ],
      [AnimationTypes.SLIDE_OUT_LEFT]: [
        { transform: 'translateX(0)', opacity: 1 },
        { transform: 'translateX(-100%)', opacity: 0 }
      ],
      [AnimationTypes.SLIDE_OUT_RIGHT]: [
        { transform: 'translateX(0)', opacity: 1 },
        { transform: 'translateX(100%)', opacity: 0 }
      ],
      [AnimationTypes.SLIDE_OUT_UP]: [
        { transform: 'translateY(0)', opacity: 1 },
        { transform: 'translateY(-100%)', opacity: 0 }
      ],
      [AnimationTypes.SLIDE_OUT_DOWN]: [
        { transform: 'translateY(0)', opacity: 1 },
        { transform: 'translateY(100%)', opacity: 0 }
      ],
      [AnimationTypes.SCALE_IN]: [
        { transform: 'scale(0)', opacity: 0 },
        { transform: 'scale(1)', opacity: 1 }
      ],
      [AnimationTypes.SCALE_OUT]: [
        { transform: 'scale(1)', opacity: 1 },
        { transform: 'scale(0)', opacity: 0 }
      ],
      [AnimationTypes.BOUNCE_IN]: [
        { transform: 'scale(0)', opacity: 0 },
        { transform: 'scale(1.1)', opacity: 1, offset: 0.8 },
        { transform: 'scale(1)', opacity: 1 }
      ],
      [AnimationTypes.SHAKE]: [
        { transform: 'translateX(0)' },
        { transform: 'translateX(-10px)' },
        { transform: 'translateX(10px)' },
        { transform: 'translateX(-10px)' },
        { transform: 'translateX(10px)' },
        { transform: 'translateX(0)' }
      ],
      [AnimationTypes.PULSE]: [
        { transform: 'scale(1)' },
        { transform: 'scale(1.05)' },
        { transform: 'scale(1)' }
      ],
      [AnimationTypes.FLIP]: [
        { transform: 'rotateY(0deg)' },
        { transform: 'rotateY(180deg)' }
      ]
    };

    return keyframes[type] || null;
  }

  /**
   * 停止所有动画
   */
  stopAllAnimations() {
    this.activeAnimations.forEach(animation => {
      animation.cancel();
    });
    this.activeAnimations.clear();
  }

  /**
   * 停止指定元素的动画
   * @param {Element} element - 目标元素
   */
  stopElementAnimations(element) {
    this.activeAnimations.forEach((animation, id) => {
      if (animation.effect && animation.effect.target === element) {
        animation.cancel();
        this.activeAnimations.delete(id);
      }
    });
  }

  /**
   * 获取活跃动画数量
   * @returns {number} 活跃动画数量
   */
  getActiveAnimationCount() {
    return this.activeAnimations.size;
  }
}

/**
 * 微交互工具类
 */
export class MicroInteractions {
  /**
   * 添加悬停效果
   * @param {Element} element - 目标元素
   * @param {object} options - 选项
   */
  static addHoverEffect(element, options = {}) {
    const {
      scale = 1.02,
      duration = AnimationDurations.FAST,
      easing = EasingFunctions.EASE_OUT
    } = options;

    element.addEventListener('mouseenter', () => {
      element.style.transition = `transform ${duration}ms ${easing}`;
      element.style.transform = `scale(${scale})`;
    });

    element.addEventListener('mouseleave', () => {
      element.style.transform = 'scale(1)';
    });
  }

  /**
   * 添加点击波纹效果
   * @param {Element} element - 目标元素
   * @param {object} options - 选项
   */
  static addRippleEffect(element, options = {}) {
    const {
      color = 'rgba(255, 255, 255, 0.3)',
      duration = 600
    } = options;

    element.addEventListener('click', (e) => {
      const rect = element.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      const ripple = document.createElement('div');
      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: ${color};
        border-radius: 50%;
        transform: scale(0);
        animation: ripple ${duration}ms ease-out;
        pointer-events: none;
      `;

      // 确保元素有相对定位
      if (getComputedStyle(element).position === 'static') {
        element.style.position = 'relative';
      }

      element.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, duration);
    });

    // 添加波纹动画CSS
    if (!document.querySelector('#ripple-styles')) {
      const style = document.createElement('style');
      style.id = 'ripple-styles';
      style.textContent = `
        @keyframes ripple {
          to {
            transform: scale(2);
            opacity: 0;
          }
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * 添加焦点环效果
   * @param {Element} element - 目标元素
   * @param {object} options - 选项
   */
  static addFocusRing(element, options = {}) {
    const {
      color = 'var(--color-primary)',
      width = '2px',
      offset = '2px'
    } = options;

    element.addEventListener('focus', () => {
      element.style.outline = `${width} solid ${color}`;
      element.style.outlineOffset = offset;
    });

    element.addEventListener('blur', () => {
      element.style.outline = 'none';
      element.style.outlineOffset = '0';
    });
  }

  /**
   * 添加加载状态效果
   * @param {Element} element - 目标元素
   * @param {boolean} loading - 是否加载中
   */
  static setLoadingState(element, loading) {
    if (loading) {
      element.classList.add('loading');
      element.disabled = true;
      
      // 添加加载动画
      if (!element.querySelector('.loading-spinner')) {
        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner';
        element.appendChild(spinner);
      }
    } else {
      element.classList.remove('loading');
      element.disabled = false;
      
      // 移除加载动画
      const spinner = element.querySelector('.loading-spinner');
      if (spinner) {
        spinner.remove();
      }
    }
  }
}

/**
 * 性能优化工具
 */
export class AnimationPerformance {
  /**
   * 检查是否支持硬件加速
   * @returns {boolean} 是否支持
   */
  static supportsHardwareAcceleration() {
    const testElement = document.createElement('div');
    testElement.style.transform = 'translateZ(0)';
    return testElement.style.transform !== '';
  }

  /**
   * 启用硬件加速
   * @param {Element} element - 目标元素
   */
  static enableHardwareAcceleration(element) {
    if (this.supportsHardwareAcceleration()) {
      element.style.transform = 'translateZ(0)';
      element.style.willChange = 'transform, opacity';
    }
  }

  /**
   * 禁用硬件加速
   * @param {Element} element - 目标元素
   */
  static disableHardwareAcceleration(element) {
    element.style.transform = '';
    element.style.willChange = 'auto';
  }

  /**
   * 检查是否应该减少动画
   * @returns {boolean} 是否应该减少动画
   */
  static shouldReduceMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }
}

// 创建全局动画管理器实例
export const globalAnimationManager = new AnimationManager();

export default {
  AnimationTypes,
  AnimationDurations,
  EasingFunctions,
  AnimationManager,
  MicroInteractions,
  AnimationPerformance,
  globalAnimationManager
};
