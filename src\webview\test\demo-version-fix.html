<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息版本载入修复演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 5px;
            border: 2px solid;
        }
        .before {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .after {
            border-color: #28a745;
            background: #d4edda;
        }
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .message-demo {
            background: #fff;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .message-content {
            font-size: 14px;
            margin-bottom: 5px;
        }
        .message-info {
            font-size: 12px;
            color: #666;
        }
        .problem-highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .solution-highlight {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>消息版本载入修复演示</h1>
        <p>这个演示展示了修复前后消息版本载入的差异。</p>

        <div class="problem-highlight">
            <h3>🐛 问题描述</h3>
            <p>当从历史记录载入具有多个版本的消息时，显示的可能不是当前版本（currentVersionIndex）对应的内容，而是过时的 message.content 字段。</p>
        </div>

        <div class="solution-highlight">
            <h3>✅ 解决方案</h3>
            <p>修改 createMessageElement 方法，使用 getCurrentVersionContent() 方法根据 currentVersionIndex 获取正确的版本内容。</p>
        </div>

        <h2>对比演示</h2>
        <div class="comparison">
            <div class="before">
                <div class="demo-title">修复前 (错误行为)</div>
                <div class="code-block">
// 直接使用 message.content
textContent: message.content
                </div>
                <div id="before-demo"></div>
                <p><strong>问题：</strong>显示的是过时的内容，不是用户期望看到的当前版本。</p>
            </div>

            <div class="after">
                <div class="demo-title">修复后 (正确行为)</div>
                <div class="code-block">
// 使用当前版本的内容
textContent: this.getCurrentVersionContent(message)
                </div>
                <div id="after-demo"></div>
                <p><strong>修复：</strong>正确显示当前版本的内容。</p>
            </div>
        </div>

        <h2>技术细节</h2>
        <div class="code-block">
getCurrentVersionContent(message) {
  // 如果消息有版本系统
  if (message.versions && message.versions.length > 0) {
    const currentIndex = message.currentVersionIndex || 0;
    // 确保索引在有效范围内
    if (currentIndex >= 0 && currentIndex < message.versions.length) {
      const currentVersion = message.versions[currentIndex];
      return currentVersion ? currentVersion.content : message.content;
    }
  }
  // 向后兼容：如果没有版本系统或索引无效，使用原始内容
  return message.content;
}
        </div>

        <h2>修复范围</h2>
        <ul>
            <li>✅ <code>createMessageElement</code> - 消息元素创建时的内容显示</li>
            <li>✅ <code>enterEditMode</code> - 进入编辑模式时的原始内容获取</li>
            <li>✅ <code>regenerateAIReply</code> - 重新生成AI回复时的内容传递</li>
            <li>✅ <code>exportMessages</code> - 导出消息时的内容获取</li>
        </ul>

        <button onclick="runDemo()">运行演示</button>
        <button onclick="showTechnicalDetails()">显示技术细节</button>
    </div>

    <script>
        // 模拟消息数据
        const sampleMessage = {
            id: 'demo-msg-001',
            content: '这是过时的内容', // 模拟过时的 message.content
            isUser: true,
            timestamp: Date.now() - 10000,
            versions: [
                { content: '原始消息内容', timestamp: Date.now() - 10000, aiReply: null },
                { content: '第一次编辑后的内容', timestamp: Date.now() - 5000, aiReply: null },
                { content: '最终编辑后的内容', timestamp: Date.now() - 1000, aiReply: null }
            ],
            currentVersionIndex: 2 // 当前应该显示第三个版本
        };

        // 修复前的行为（错误）
        function createMessageElementBefore(message) {
            return {
                content: message.content, // 直接使用 message.content
                displayText: message.content
            };
        }

        // 修复后的行为（正确）
        function getCurrentVersionContent(message) {
            if (message.versions && message.versions.length > 0) {
                const currentIndex = message.currentVersionIndex || 0;
                if (currentIndex >= 0 && currentIndex < message.versions.length) {
                    const currentVersion = message.versions[currentIndex];
                    return currentVersion ? currentVersion.content : message.content;
                }
            }
            return message.content;
        }

        function createMessageElementAfter(message) {
            return {
                content: getCurrentVersionContent(message),
                displayText: getCurrentVersionContent(message)
            };
        }

        function runDemo() {
            // 修复前的演示
            const beforeResult = createMessageElementBefore(sampleMessage);
            const beforeDiv = document.getElementById('before-demo');
            beforeDiv.innerHTML = `
                <div class="message-demo">
                    <div class="message-content">"${beforeResult.displayText}"</div>
                    <div class="message-info">
                        当前版本索引: ${sampleMessage.currentVersionIndex}<br>
                        总版本数: ${sampleMessage.versions.length}<br>
                        期望显示: "${sampleMessage.versions[sampleMessage.currentVersionIndex].content}"
                    </div>
                </div>
            `;

            // 修复后的演示
            const afterResult = createMessageElementAfter(sampleMessage);
            const afterDiv = document.getElementById('after-demo');
            afterDiv.innerHTML = `
                <div class="message-demo">
                    <div class="message-content">"${afterResult.displayText}"</div>
                    <div class="message-info">
                        当前版本索引: ${sampleMessage.currentVersionIndex}<br>
                        总版本数: ${sampleMessage.versions.length}<br>
                        正确显示: "${sampleMessage.versions[sampleMessage.currentVersionIndex].content}"
                    </div>
                </div>
            `;
        }

        function showTechnicalDetails() {
            alert(`
技术细节：

问题根源：
- createMessageElement 方法中直接使用 message.content
- message.content 可能不是当前版本的内容
- 导致历史记录载入时显示错误版本

解决方案：
- 添加 getCurrentVersionContent 辅助方法
- 根据 currentVersionIndex 获取正确版本内容
- 向后兼容没有版本系统的旧消息

影响范围：
- 历史记录载入
- 消息编辑
- AI回复重新生成
- 消息导出
            `);
        }

        // 页面加载时自动运行演示
        document.addEventListener('DOMContentLoaded', runDemo);
    </script>
</body>
</html>
