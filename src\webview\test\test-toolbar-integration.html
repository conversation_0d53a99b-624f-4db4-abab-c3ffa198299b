<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具栏操作通知集成测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
        }
        
        .demo-panel {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-lg);
        }
        
        .message-actions-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .action-button {
            padding: var(--spacing-md);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            background: var(--color-surface);
            cursor: pointer;
            text-align: center;
            transition: all var(--transition-fast);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .action-button:hover {
            border-color: var(--color-primary);
            background: var(--color-surface-hover);
        }
        
        .action-icon {
            font-size: 24px;
            margin-bottom: var(--spacing-xs);
        }
        
        .action-label {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
        }
        
        .action-description {
            font-size: var(--font-size-xs);
            color: var(--color-foreground-muted);
        }
        
        .test-messages {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: var(--spacing-lg);
        }
        
        .test-input {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .test-input input {
            flex: 1;
        }
        
        .stats-display {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            max-height: 200px;
            overflow-y: auto;
        }
        
        .scenario-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .scenario-card {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .scenario-card:hover {
            border-color: var(--color-primary);
            background: var(--color-primary-background);
        }
        
        .scenario-title {
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--spacing-xs);
            color: var(--color-foreground);
        }
        
        .scenario-description {
            font-size: var(--font-size-sm);
            color: var(--color-foreground-muted);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>工具栏操作通知集成测试</h1>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="initializeTestData()">初始化测试数据</button>
                <button class="btn btn-secondary" onclick="testAllOperations()">测试所有操作</button>
                <button class="btn btn-secondary" onclick="testErrorScenarios()">测试错误场景</button>
                <button class="btn btn-secondary" onclick="testBatchOperations()">测试批量操作</button>
                <button class="btn btn-ghost" onclick="clearTestData()">清空测试数据</button>
                <button class="btn btn-ghost" onclick="clearNotifications()">清空通知</button>
            </div>
        </div>
        
        <!-- 消息操作测试 -->
        <div class="test-section">
            <h2>消息操作测试</h2>
            <div class="message-actions-demo">
                <div class="action-button" onclick="testCopyMessage()">
                    <div class="action-icon">📋</div>
                    <div class="action-label">复制消息</div>
                    <div class="action-description">测试复制功能</div>
                </div>
                
                <div class="action-button" onclick="testEditMessage()">
                    <div class="action-icon">✏️</div>
                    <div class="action-label">编辑消息</div>
                    <div class="action-description">测试编辑功能</div>
                </div>
                
                <div class="action-button" onclick="testDeleteMessage()">
                    <div class="action-icon">🗑️</div>
                    <div class="action-label">删除消息</div>
                    <div class="action-description">测试删除功能</div>
                </div>
                
                <div class="action-button" onclick="testSearchMessages()">
                    <div class="action-icon">🔍</div>
                    <div class="action-label">搜索消息</div>
                    <div class="action-description">测试搜索功能</div>
                </div>
                
                <div class="action-button" onclick="testExportMessages()">
                    <div class="action-icon">📤</div>
                    <div class="action-label">导出消息</div>
                    <div class="action-description">测试导出功能</div>
                </div>
                
                <div class="action-button" onclick="testClearMessages()">
                    <div class="action-icon">🧹</div>
                    <div class="action-label">清空消息</div>
                    <div class="action-description">测试清空功能</div>
                </div>
            </div>
        </div>
        
        <!-- 场景测试 -->
        <div class="test-section">
            <h2>场景测试</h2>
            <div class="scenario-list">
                <div class="scenario-card" onclick="testSuccessScenario()">
                    <div class="scenario-title">成功场景</div>
                    <div class="scenario-description">测试所有操作的成功流程</div>
                </div>
                
                <div class="scenario-card" onclick="testErrorScenario()">
                    <div class="scenario-title">错误场景</div>
                    <div class="scenario-description">测试操作失败的处理</div>
                </div>
                
                <div class="scenario-card" onclick="testConfirmScenario()">
                    <div class="scenario-title">确认场景</div>
                    <div class="scenario-description">测试需要确认的操作</div>
                </div>
                
                <div class="scenario-card" onclick="testProgressScenario()">
                    <div class="scenario-title">进度场景</div>
                    <div class="scenario-description">测试长时间操作的进度</div>
                </div>
                
                <div class="scenario-card" onclick="testBatchScenario()">
                    <div class="scenario-title">批量场景</div>
                    <div class="scenario-description">测试批量操作的反馈</div>
                </div>
                
                <div class="scenario-card" onclick="testValidationScenario()">
                    <div class="scenario-title">验证场景</div>
                    <div class="scenario-description">测试输入验证的反馈</div>
                </div>
            </div>
        </div>
        
        <!-- 演示区域 -->
        <div class="test-section">
            <h2>演示区域</h2>
            <div class="demo-grid">
                <!-- 消息列表 -->
                <div class="demo-panel">
                    <h3>测试消息</h3>
                    <div id="testMessages" class="test-messages">
                        <!-- 测试消息将在这里显示 -->
                    </div>
                    
                    <div class="test-input">
                        <input type="text" id="newMessageText" class="input" placeholder="输入新消息内容">
                        <button class="btn btn-primary" onclick="addTestMessage()">添加消息</button>
                    </div>
                </div>
                
                <!-- 操作统计 -->
                <div class="demo-panel">
                    <h3>操作统计</h3>
                    <div id="operationStats" class="stats-display">
                        <!-- 操作统计将在这里显示 -->
                    </div>
                    <button class="btn btn-secondary" onclick="refreshStats()">刷新统计</button>
                </div>
            </div>
        </div>
        
        <!-- 自定义测试 -->
        <div class="test-section">
            <h2>自定义测试</h2>
            <div class="test-input">
                <select id="testAction" class="input">
                    <option value="copy">复制</option>
                    <option value="edit">编辑</option>
                    <option value="delete">删除</option>
                    <option value="save">保存</option>
                    <option value="export">导出</option>
                    <option value="import">导入</option>
                    <option value="search">搜索</option>
                    <option value="reset">重置</option>
                </select>
                
                <select id="testType" class="input">
                    <option value="success">成功</option>
                    <option value="error">错误</option>
                    <option value="warning">警告</option>
                    <option value="info">信息</option>
                    <option value="loading">加载</option>
                </select>
                
                <input type="text" id="customMessage" class="input" placeholder="自定义消息（可选）">
                <button class="btn btn-primary" onclick="testCustomNotification()">测试通知</button>
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { Store } from './scripts/state/store.js';
        import { MessageComponent } from './scripts/components/message.js';
        import { globalToolbarNotifications, ToolbarActions } from './scripts/components/toolbar-notifications.js';
        import { globalNotificationManager } from './scripts/components/notification-manager.js';
        import { EventManager } from './scripts/utils/events.js';
        import { MessageTypes, MessageStatus } from './scripts/components/message-manager.js';
        
        // 初始化组件
        const store = new Store();
        const eventManager = new EventManager();
        const messageComponent = new MessageComponent(store, eventManager);
        
        // 全局变量
        window.store = store;
        window.messageComponent = messageComponent;
        window.toolbarNotifications = globalToolbarNotifications;
        window.notificationManager = globalNotificationManager;
        window.testStats = {
            operations: 0,
            successes: 0,
            errors: 0,
            warnings: 0,
            confirmations: 0
        };
        
        // 初始化
        Promise.all([
            messageComponent.init(),
            globalNotificationManager.init()
        ]).then(() => {
            console.log('工具栏操作通知集成测试页面初始化完成');
            setupEventListeners();
            refreshStats();
        }).catch(error => {
            console.error('初始化失败:', error);
        });
        
        // 设置事件监听
        function setupEventListeners() {
            // 监听工具栏通知事件
            globalToolbarNotifications.getEventManager().on('toolbarNotification', (data) => {
                testStats.operations++;
                
                switch (data.type) {
                    case 'success':
                        testStats.successes++;
                        break;
                    case 'error':
                        testStats.errors++;
                        break;
                    case 'warning':
                        testStats.warnings++;
                        break;
                }
                
                refreshStats();
            });
            
            // 监听消息组件事件
            messageComponent.on('messageAdded', () => refreshStats());
            messageComponent.on('messageDeleted', () => refreshStats());
            messageComponent.on('messageUpdated', () => refreshStats());
            messageComponent.on('messagesCleared', () => refreshStats());
        }
        
        // 刷新统计
        function refreshStats() {
            const messages = store.getState('messages') || [];
            const notificationStats = globalNotificationManager.getStatistics();
            const toolbarStats = globalToolbarNotifications.getStatistics();
            
            const statsDisplay = document.getElementById('operationStats');
            statsDisplay.innerHTML = `
消息统计:
- 总消息数: ${messages.length}
- 用户消息: ${messages.filter(m => m.isUser).length}
- AI消息: ${messages.filter(m => !m.isUser).length}

通知统计:
- 活跃通知: ${notificationStats.active}
- 队列通知: ${notificationStats.queued}
- 总通知数: ${notificationStats.total}

操作统计:
- 总操作数: ${testStats.operations}
- 成功操作: ${testStats.successes}
- 错误操作: ${testStats.errors}
- 警告操作: ${testStats.warnings}

工具栏历史:
- 历史记录: ${toolbarStats.total}
            `;
        }
        
        // 测试函数
        window.initializeTestData = function() {
            const testMessages = [
                { content: '这是第一条测试消息', isUser: true },
                { content: '这是AI的回复消息', isUser: false },
                { content: '这是一条很长的用户消息，用来测试各种操作功能，包括复制、编辑、删除等。', isUser: true },
                { content: '这是另一条AI回复', isUser: false },
                { content: '最后一条测试消息', isUser: true }
            ];
            
            testMessages.forEach(messageData => {
                messageComponent.addMessage({
                    ...messageData,
                    type: MessageTypes.TEXT,
                    status: MessageStatus.SENT
                });
            });
            
            globalToolbarNotifications.notifySuccess(ToolbarActions.IMPORT, `初始化了 ${testMessages.length} 条测试消息`);
        };
        
        window.testCopyMessage = function() {
            const messages = store.getState('messages') || [];
            if (messages.length === 0) {
                globalToolbarNotifications.notifyWarning(ToolbarActions.COPY, '没有消息可以复制');
                return;
            }
            
            // 模拟复制第一条消息
            const firstMessage = messages[0];
            navigator.clipboard.writeText(firstMessage.content).then(() => {
                globalToolbarNotifications.notifySuccess(ToolbarActions.COPY);
            }).catch(() => {
                globalToolbarNotifications.notifyError(ToolbarActions.COPY, '复制失败，请重试');
            });
        };
        
        window.testEditMessage = function() {
            const messages = store.getState('messages') || [];
            const userMessages = messages.filter(m => m.isUser);
            
            if (userMessages.length === 0) {
                globalToolbarNotifications.notifyWarning(ToolbarActions.EDIT, '没有用户消息可以编辑');
                return;
            }
            
            // 模拟编辑流程
            globalToolbarNotifications.notifyInfo(ToolbarActions.EDIT, '进入编辑模式');
            
            setTimeout(() => {
                globalToolbarNotifications.notifySuccess(ToolbarActions.EDIT, '消息编辑已保存');
            }, 2000);
        };
        
        window.testDeleteMessage = function() {
            const messages = store.getState('messages') || [];
            if (messages.length === 0) {
                globalToolbarNotifications.notifyWarning(ToolbarActions.DELETE, '没有消息可以删除');
                return;
            }
            
            // 使用确认对话框
            globalToolbarNotifications.notifyConfirm(
                ToolbarActions.DELETE,
                () => {
                    // 模拟删除
                    globalToolbarNotifications.notifySuccess(ToolbarActions.DELETE, '消息已删除');
                },
                () => {
                    globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '删除操作已取消');
                }
            );
        };
        
        window.testSearchMessages = function() {
            const query = '测试';
            
            // 模拟搜索
            const loadingId = globalToolbarNotifications.notifyLoading(ToolbarActions.SEARCH, '正在搜索消息...');
            
            setTimeout(() => {
                globalNotificationManager.hide(loadingId);
                globalToolbarNotifications.notifySuccess(ToolbarActions.SEARCH, `找到 3 条包含"${query}"的消息`);
            }, 1500);
        };
        
        window.testExportMessages = function() {
            const messages = store.getState('messages') || [];
            if (messages.length === 0) {
                globalToolbarNotifications.notifyWarning(ToolbarActions.EXPORT, '没有消息可以导出');
                return;
            }
            
            // 模拟导出
            const loadingId = globalToolbarNotifications.notifyLoading(ToolbarActions.EXPORT, '正在导出聊天记录...');
            
            setTimeout(() => {
                globalNotificationManager.hide(loadingId);
                globalToolbarNotifications.notifySuccess(ToolbarActions.EXPORT, `成功导出 ${messages.length} 条消息`);
            }, 2000);
        };
        
        window.testClearMessages = function() {
            const messages = store.getState('messages') || [];
            if (messages.length === 0) {
                globalToolbarNotifications.notifyInfo(ToolbarActions.DELETE, '没有消息需要清空');
                return;
            }
            
            // 使用确认对话框
            globalToolbarNotifications.notifyConfirm(
                ToolbarActions.RESET,
                () => {
                    messageComponent.performClearMessages();
                },
                () => {
                    globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '清空操作已取消');
                },
                `确定要清空所有 ${messages.length} 条消息吗？`
            );
        };
        
        window.testAllOperations = function() {
            const operations = [
                () => testCopyMessage(),
                () => testEditMessage(),
                () => testSearchMessages(),
                () => testExportMessages()
            ];
            
            operations.forEach((operation, index) => {
                setTimeout(operation, index * 1000);
            });
        };
        
        window.testCustomNotification = function() {
            const action = document.getElementById('testAction').value;
            const type = document.getElementById('testType').value;
            const customMessage = document.getElementById('customMessage').value;
            
            globalToolbarNotifications.notify(action, type, customMessage || null);
        };
        
        window.addTestMessage = function() {
            const text = document.getElementById('newMessageText').value.trim();
            if (!text) {
                globalToolbarNotifications.notifyWarning(ToolbarActions.EDIT, '请输入消息内容');
                return;
            }
            
            messageComponent.addMessage({
                content: text,
                isUser: true,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            document.getElementById('newMessageText').value = '';
            globalToolbarNotifications.notifySuccess(ToolbarActions.SAVE, '消息已添加');
        };
        
        window.clearTestData = function() {
            messageComponent.performClearMessages();
        };
        
        window.clearNotifications = function() {
            globalNotificationManager.clear();
        };
        
        window.refreshStats = refreshStats;
        
        console.log('工具栏操作通知集成测试页面已加载');
    </script>
</body>
</html>
