<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI回复管理测试 - </title>
    <link rel="stylesheet" href="../styles/variables.css">
    <link rel="stylesheet" href="../styles/components.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #218838;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        .status-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-label {
            font-weight: bold;
        }
        .status-value {
            color: #666;
        }
        .log-display {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info {
            color: #63b3ed;
        }
        .log-entry.success {
            color: #68d391;
        }
        .log-entry.error {
            color: #fc8181;
        }
        .log-entry.warning {
            color: #fbb6ce;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI回复管理测试 - </h1>
        <p>测试新的AI回复管理系统，包括状态管理、流式更新、错误处理等功能</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. AI回复生成测试</h3>
            <div class="controls">
                <button class="button" onclick="testBasicGeneration()">基础生成测试</button>
                <button class="button" onclick="testStreamingGeneration()">流式生成测试</button>
                <button class="button" onclick="testMultipleGeneration()">多消息生成测试</button>
                <button class="button danger" onclick="testGenerationError()">错误生成测试</button>
            </div>
            <div id="generationResult" class="result"></div>
            <div id="generationStatus" class="status-display" style="display: none;">
                <h4>生成状态</h4>
                <div id="generationStatusContent"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. 状态管理测试</h3>
            <div class="controls">
                <button class="button" onclick="testStatusTransitions()">状态转换测试</button>
                <button class="button" onclick="testProgressUpdate()">进度更新测试</button>
                <button class="button danger" onclick="testCancelGeneration()">取消生成测试</button>
                <button class="button" onclick="testRetryGeneration()">重试生成测试</button>
            </div>
            <div id="statusResult" class="result"></div>
            <div id="statusDisplay" class="status-display" style="display: none;">
                <h4>当前状态</h4>
                <div id="statusContent"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>3. 错误处理测试</h3>
            <div class="controls">
                <button class="button danger" onclick="testNetworkError()">网络错误</button>
                <button class="button danger" onclick="testTimeoutError()">超时错误</button>
                <button class="button danger" onclick="testAPIError()">API错误</button>
                <button class="button success" onclick="testErrorRecovery()">错误恢复</button>
            </div>
            <div id="errorResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 批量操作测试</h3>
            <div class="controls">
                <button class="button" onclick="testBatchGeneration()">批量生成</button>
                <button class="button danger" onclick="testCancelAll()">取消所有</button>
                <button class="button" onclick="testCleanupFailed()">清理失败回复</button>
                <button class="button" onclick="testGetActiveReplies()">获取活跃回复</button>
            </div>
            <div id="batchResult" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>实时日志</h2>
        <div class="controls">
            <button class="button" onclick="clearLog()">清空日志</button>
            <button class="button" onclick="exportLog()">导出日志</button>
        </div>
        <div id="logDisplay" class="log-display"></div>
    </div>

    <script type="module">
        // 模拟AI回复管理器
        class AIReplyManager {
            constructor() {
                this.messages = [];
                this.activeReplies = new Map();
                this.logEntries = [];
            }

            // 创建测试消息
            createTestMessage(content) {
                const message = {
                    id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    isUser: true,
                    timestamp: Date.now(),
                    versions: [{
                        id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                        content: content,
                        timestamp: Date.now(),
                        aiReply: null,
                        metadata: {}
                    }],
                    currentVersionIndex: 0,
                    metadata: {}
                };
                this.messages.push(message);
                return message;
            }

            // 开始AI回复生成
            startAIReply(message, options = {}) {
                const currentVersion = message.versions[message.currentVersionIndex];
                const aiReply = {
                    id: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    content: '',
                    timestamp: Date.now(),
                    startTimestamp: Date.now(),
                    endTimestamp: null,
                    status: 'pending',
                    progress: 0,
                    metadata: {
                        model: 'GPT-4-Test',
                        requestId: `req_${Date.now()}`,
                        retryCount: 0,
                        ...options.metadata
                    }
                };

                currentVersion.aiReply = aiReply;
                this.activeReplies.set(message.id, aiReply);
                
                this.log('info', `开始生成AI回复: ${message.id}`);
                return aiReply;
            }

            // 更新AI回复状态
            updateAIReplyStatus(messageId, status, updates = {}) {
                const message = this.messages.find(m => m.id === messageId);
                if (!message) return;

                const currentVersion = message.versions[message.currentVersionIndex];
                if (!currentVersion || !currentVersion.aiReply) return;

                const aiReply = currentVersion.aiReply;
                const oldStatus = aiReply.status;
                
                aiReply.status = status;
                aiReply.timestamp = Date.now();
                Object.assign(aiReply, updates);

                // 根据状态自动设置字段
                switch (status) {
                    case 'generating':
                        aiReply.progress = Math.max(0, Math.min(99, updates.progress || 0));
                        break;
                    case 'completed':
                        aiReply.endTimestamp = Date.now();
                        aiReply.progress = 100;
                        aiReply.metadata.duration = aiReply.endTimestamp - aiReply.startTimestamp;
                        this.activeReplies.delete(messageId);
                        break;
                    case 'error':
                    case 'cancelled':
                    case 'timeout':
                        aiReply.endTimestamp = Date.now();
                        aiReply.metadata.duration = aiReply.endTimestamp - aiReply.startTimestamp;
                        this.activeReplies.delete(messageId);
                        break;
                }

                this.log('info', `状态变化: ${messageId} ${oldStatus} -> ${status} (${aiReply.progress}%)`);
                this.updateStatusDisplay();
            }

            // 流式更新内容
            streamContent(messageId, contentChunk, isComplete = false) {
                const message = this.messages.find(m => m.id === messageId);
                if (!message) return;

                const currentVersion = message.versions[message.currentVersionIndex];
                if (!currentVersion || !currentVersion.aiReply) return;

                const aiReply = currentVersion.aiReply;
                aiReply.content += contentChunk;

                if (isComplete) {
                    this.updateAIReplyStatus(messageId, 'completed');
                } else {
                    const progress = Math.min(95, Math.floor(aiReply.content.length / 10));
                    this.updateAIReplyStatus(messageId, 'generating', { progress });
                }

                this.log('info', `流式更新: ${messageId} +${contentChunk.length}字符`);
            }

            // 模拟错误
            simulateError(messageId, errorType) {
                const errorMessages = {
                    network_error: '网络连接失败',
                    timeout_error: '请求超时',
                    api_error: 'API调用失败',
                    unknown_error: '未知错误'
                };

                this.updateAIReplyStatus(messageId, 'error', {
                    content: '',
                    metadata: {
                        error: errorMessages[errorType] || '未知错误',
                        errorType: errorType
                    }
                });

                this.log('error', `生成错误: ${messageId} - ${errorMessages[errorType]}`);
            }

            // 取消生成
            cancelGeneration(messageId) {
                this.updateAIReplyStatus(messageId, 'cancelled', {
                    content: '',
                    progress: 0
                });
                this.log('warning', `取消生成: ${messageId}`);
            }

            // 获取活跃回复
            getActiveReplies() {
                return Array.from(this.activeReplies.entries()).map(([messageId, aiReply]) => ({
                    messageId,
                    aiReply
                }));
            }

            // 取消所有活跃回复
            cancelAllActive() {
                const active = this.getActiveReplies();
                active.forEach(({ messageId }) => {
                    this.cancelGeneration(messageId);
                });
                this.log('warning', `取消了 ${active.length} 个活跃回复`);
            }

            // 清理失败回复
            cleanupFailed() {
                let cleanedCount = 0;
                this.messages.forEach(message => {
                    const currentVersion = message.versions[message.currentVersionIndex];
                    if (currentVersion && currentVersion.aiReply) {
                        const aiReply = currentVersion.aiReply;
                        if (['error', 'cancelled', 'timeout'].includes(aiReply.status)) {
                            currentVersion.aiReply = null;
                            cleanedCount++;
                        }
                    }
                });
                this.log('success', `清理了 ${cleanedCount} 个失败回复`);
                this.updateStatusDisplay();
            }

            // 日志记录
            log(level, message) {
                const entry = {
                    timestamp: new Date().toLocaleTimeString(),
                    level,
                    message
                };
                this.logEntries.push(entry);
                this.updateLogDisplay();
            }

            // 更新状态显示
            updateStatusDisplay() {
                const activeCount = this.activeReplies.size;
                const totalMessages = this.messages.length;
                const completedCount = this.messages.filter(m => {
                    const version = m.versions[m.currentVersionIndex];
                    return version && version.aiReply && version.aiReply.status === 'completed';
                }).length;

                const statusContent = document.getElementById('statusContent');
                if (statusContent) {
                    statusContent.innerHTML = `
                        <div class="status-item">
                            <span class="status-label">总消息数:</span>
                            <span class="status-value">${totalMessages}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">活跃回复:</span>
                            <span class="status-value">${activeCount}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">已完成:</span>
                            <span class="status-value">${completedCount}</span>
                        </div>
                    `;
                }

                const statusDisplay = document.getElementById('statusDisplay');
                if (statusDisplay) {
                    statusDisplay.style.display = 'block';
                }
            }

            // 更新日志显示
            updateLogDisplay() {
                const logDisplay = document.getElementById('logDisplay');
                if (logDisplay) {
                    const recentEntries = this.logEntries.slice(-50); // 只显示最近50条
                    logDisplay.innerHTML = recentEntries.map(entry => 
                        `<div class="log-entry ${entry.level}">[${entry.timestamp}] ${entry.message}</div>`
                    ).join('');
                    logDisplay.scrollTop = logDisplay.scrollHeight;
                }
            }
        }

        // 全局管理器实例
        const aiManager = new AIReplyManager();

        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 导出测试函数到全局作用域
        window.testBasicGeneration = testBasicGeneration;
        window.testStreamingGeneration = testStreamingGeneration;
        window.testMultipleGeneration = testMultipleGeneration;
        window.testGenerationError = testGenerationError;
        window.testStatusTransitions = testStatusTransitions;
        window.testProgressUpdate = testProgressUpdate;
        window.testCancelGeneration = testCancelGeneration;
        window.testRetryGeneration = testRetryGeneration;
        window.testNetworkError = testNetworkError;
        window.testTimeoutError = testTimeoutError;
        window.testAPIError = testAPIError;
        window.testErrorRecovery = testErrorRecovery;
        window.testBatchGeneration = testBatchGeneration;
        window.testCancelAll = testCancelAll;
        window.testCleanupFailed = testCleanupFailed;
        window.testGetActiveReplies = testGetActiveReplies;
        window.clearLog = clearLog;
        window.exportLog = exportLog;

        // 1. AI回复生成测试
        function testBasicGeneration() {
            const message = aiManager.createTestMessage('这是一个基础测试消息');
            const aiReply = aiManager.startAIReply(message);

            // 模拟生成过程
            setTimeout(() => {
                aiManager.updateAIReplyStatus(message.id, 'generating', { progress: 30 });
            }, 500);

            setTimeout(() => {
                aiManager.updateAIReplyStatus(message.id, 'generating', {
                    progress: 70,
                    content: '正在生成回复...'
                });
            }, 1000);

            setTimeout(() => {
                aiManager.updateAIReplyStatus(message.id, 'completed', {
                    content: '这是一个完整的AI回复内容。'
                });
            }, 1500);

            showResult('generationResult', '✅ 基础生成测试已开始，请观察状态变化', 'success');
        }

        function testStreamingGeneration() {
            const message = aiManager.createTestMessage('测试流式生成');
            const aiReply = aiManager.startAIReply(message);

            const chunks = [
                '这是', '一个', '流式', '生成', '的', 'AI', '回复', '内容', '。',
                '每个', '片段', '都会', '逐步', '添加', '到', '回复', '中', '。'
            ];

            aiManager.updateAIReplyStatus(message.id, 'generating', { progress: 10 });

            chunks.forEach((chunk, index) => {
                setTimeout(() => {
                    const isComplete = index === chunks.length - 1;
                    aiManager.streamContent(message.id, chunk, isComplete);
                }, (index + 1) * 200);
            });

            showResult('generationResult', '✅ 流式生成测试已开始', 'success');
        }

        function testMultipleGeneration() {
            const messages = [
                '第一个测试消息',
                '第二个测试消息',
                '第三个测试消息'
            ].map(content => aiManager.createTestMessage(content));

            messages.forEach((message, index) => {
                setTimeout(() => {
                    const aiReply = aiManager.startAIReply(message);

                    setTimeout(() => {
                        aiManager.updateAIReplyStatus(message.id, 'generating', { progress: 50 });
                    }, 300);

                    setTimeout(() => {
                        aiManager.updateAIReplyStatus(message.id, 'completed', {
                            content: `这是第${index + 1}个消息的AI回复。`
                        });
                    }, 800);
                }, index * 200);
            });

            showResult('generationResult', '✅ 多消息生成测试已开始', 'success');
        }

        function testGenerationError() {
            const message = aiManager.createTestMessage('这个消息会导致错误');
            const aiReply = aiManager.startAIReply(message);

            setTimeout(() => {
                aiManager.updateAIReplyStatus(message.id, 'generating', { progress: 20 });
            }, 300);

            setTimeout(() => {
                aiManager.simulateError(message.id, 'api_error');
            }, 800);

            showResult('generationResult', '✅ 错误生成测试已开始', 'success');
        }

        // 2. 状态管理测试
        function testStatusTransitions() {
            const message = aiManager.createTestMessage('状态转换测试');
            const aiReply = aiManager.startAIReply(message);

            const states = [
                { status: 'generating', progress: 25, delay: 500 },
                { status: 'generating', progress: 50, delay: 1000 },
                { status: 'generating', progress: 75, delay: 1500 },
                { status: 'completed', progress: 100, content: '状态转换测试完成', delay: 2000 }
            ];

            states.forEach(state => {
                setTimeout(() => {
                    aiManager.updateAIReplyStatus(message.id, state.status, {
                        progress: state.progress,
                        content: state.content || '正在生成...'
                    });
                }, state.delay);
            });

            showResult('statusResult', '✅ 状态转换测试已开始', 'success');
        }

        function testProgressUpdate() {
            const message = aiManager.createTestMessage('进度更新测试');
            const aiReply = aiManager.startAIReply(message);

            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                if (progress >= 100) {
                    aiManager.updateAIReplyStatus(message.id, 'completed', {
                        progress: 100,
                        content: '进度更新测试完成'
                    });
                    clearInterval(interval);
                } else {
                    aiManager.updateAIReplyStatus(message.id, 'generating', {
                        progress: progress,
                        content: `进度: ${progress}%`
                    });
                }
            }, 200);

            showResult('statusResult', '✅ 进度更新测试已开始', 'success');
        }

        function testCancelGeneration() {
            const message = aiManager.createTestMessage('取消生成测试');
            const aiReply = aiManager.startAIReply(message);

            setTimeout(() => {
                aiManager.updateAIReplyStatus(message.id, 'generating', { progress: 30 });
            }, 300);

            setTimeout(() => {
                aiManager.cancelGeneration(message.id);
            }, 800);

            showResult('statusResult', '✅ 取消生成测试已开始', 'success');
        }

        function testRetryGeneration() {
            const message = aiManager.createTestMessage('重试生成测试');
            const aiReply = aiManager.startAIReply(message);

            // 第一次失败
            setTimeout(() => {
                aiManager.simulateError(message.id, 'network_error');
            }, 500);

            // 重试
            setTimeout(() => {
                const newAiReply = aiManager.startAIReply(message, {
                    metadata: { retryCount: 1 }
                });

                setTimeout(() => {
                    aiManager.updateAIReplyStatus(message.id, 'completed', {
                        content: '重试成功的AI回复'
                    });
                }, 800);
            }, 1500);

            showResult('statusResult', '✅ 重试生成测试已开始', 'success');
        }

        // 3. 错误处理测试
        function testNetworkError() {
            const message = aiManager.createTestMessage('网络错误测试');
            const aiReply = aiManager.startAIReply(message);

            setTimeout(() => {
                aiManager.simulateError(message.id, 'network_error');
            }, 500);

            showResult('errorResult', '✅ 网络错误测试完成', 'success');
        }

        function testTimeoutError() {
            const message = aiManager.createTestMessage('超时错误测试');
            const aiReply = aiManager.startAIReply(message);

            setTimeout(() => {
                aiManager.updateAIReplyStatus(message.id, 'generating', { progress: 10 });
            }, 300);

            setTimeout(() => {
                aiManager.simulateError(message.id, 'timeout_error');
            }, 1000);

            showResult('errorResult', '✅ 超时错误测试完成', 'success');
        }

        function testAPIError() {
            const message = aiManager.createTestMessage('API错误测试');
            const aiReply = aiManager.startAIReply(message);

            setTimeout(() => {
                aiManager.simulateError(message.id, 'api_error');
            }, 200);

            showResult('errorResult', '✅ API错误测试完成', 'success');
        }

        function testErrorRecovery() {
            const message = aiManager.createTestMessage('错误恢复测试');

            // 第一次失败
            let aiReply = aiManager.startAIReply(message);
            setTimeout(() => {
                aiManager.simulateError(message.id, 'network_error');
            }, 300);

            // 恢复
            setTimeout(() => {
                aiReply = aiManager.startAIReply(message, {
                    metadata: { retryCount: 1 }
                });

                setTimeout(() => {
                    aiManager.updateAIReplyStatus(message.id, 'completed', {
                        content: '错误恢复成功！'
                    });
                }, 500);
            }, 1000);

            showResult('errorResult', '✅ 错误恢复测试已开始', 'success');
        }

        // 4. 批量操作测试
        function testBatchGeneration() {
            const count = 5;
            const messages = Array.from({ length: count }, (_, i) =>
                aiManager.createTestMessage(`批量消息 ${i + 1}`)
            );

            messages.forEach((message, index) => {
                setTimeout(() => {
                    const aiReply = aiManager.startAIReply(message);

                    setTimeout(() => {
                        aiManager.updateAIReplyStatus(message.id, 'completed', {
                            content: `批量回复 ${index + 1}`
                        });
                    }, 500 + Math.random() * 1000);
                }, index * 100);
            });

            showResult('batchResult', `✅ 批量生成 ${count} 个回复已开始`, 'success');
        }

        function testCancelAll() {
            // 先创建一些活跃的回复
            const messages = Array.from({ length: 3 }, (_, i) =>
                aiManager.createTestMessage(`待取消消息 ${i + 1}`)
            );

            messages.forEach(message => {
                aiManager.startAIReply(message);
                aiManager.updateAIReplyStatus(message.id, 'generating', { progress: 30 });
            });

            setTimeout(() => {
                aiManager.cancelAllActive();
            }, 500);

            showResult('batchResult', '✅ 取消所有活跃回复测试完成', 'success');
        }

        function testCleanupFailed() {
            // 创建一些失败的回复
            const messages = Array.from({ length: 3 }, (_, i) =>
                aiManager.createTestMessage(`失败消息 ${i + 1}`)
            );

            messages.forEach((message, index) => {
                const aiReply = aiManager.startAIReply(message);
                const errorTypes = ['error', 'cancelled', 'timeout'];
                aiManager.simulateError(message.id, errorTypes[index] + '_error');
            });

            setTimeout(() => {
                aiManager.cleanupFailed();
            }, 500);

            showResult('batchResult', '✅ 清理失败回复测试完成', 'success');
        }

        function testGetActiveReplies() {
            // 创建一些活跃回复
            const messages = Array.from({ length: 2 }, (_, i) =>
                aiManager.createTestMessage(`活跃消息 ${i + 1}`)
            );

            messages.forEach(message => {
                aiManager.startAIReply(message);
                aiManager.updateAIReplyStatus(message.id, 'generating', { progress: 50 });
            });

            const activeReplies = aiManager.getActiveReplies();
            showResult('batchResult', `✅ 当前有 ${activeReplies.length} 个活跃回复`, 'success');
        }

        // 日志操作
        function clearLog() {
            aiManager.logEntries = [];
            aiManager.updateLogDisplay();
        }

        function exportLog() {
            const logText = aiManager.logEntries.map(entry =>
                `[${entry.timestamp}] ${entry.level.toUpperCase()}: ${entry.message}`
            ).join('\n');

            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ai-reply-test-log-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 初始化
        aiManager.updateStatusDisplay();
        aiManager.log('info', 'AI回复管理测试系统已初始化');
    </script>
</body>
</html>
    </script>
</body>
</html>
