/**
 * 字号调整组件
 * 提供可视化的字号调整和实时预览功能
 */

import BaseComponent from './base.js';
import { DOMUtils } from '../utils/dom.js';

/**
 * 字号调整组件类
 */
export class FontSizeAdjusterComponent extends BaseComponent {
  constructor(container, options = {}) {
    super(container, {
      className: 'font-size-adjuster-component',
      autoInit: true,
      ...options
    });
    
    this.fontSizes = [
      { id: 'xs', name: '极小', size: '11px', description: '适合高分辨率屏幕' },
      { id: 'sm', name: '小', size: '12px', description: '紧凑的文字显示' },
      { id: 'md', name: '中等', size: '14px', description: '标准的文字大小' },
      { id: 'lg', name: '大', size: '16px', description: '舒适的阅读体验' },
      { id: 'xl', name: '极大', size: '18px', description: '更大的文字显示' },
      { id: '2xl', name: '超大', size: '20px', description: '最大的文字大小' }
    ];
    
    this.currentFontSize = 'md';
    this.currentIndex = 2;
    
    // 绑定方法上下文
    this.setFontSize = this.setFontSize.bind(this);
    this.adjustFontSize = this.adjustFontSize.bind(this);
    this.updateSlider = this.updateSlider.bind(this);
  }

  /**
   * 获取初始状态
   */
  getInitialState() {
    return {
      ...super.getInitialState(),
      fontSize: 'md',
      fontIndex: 2,
      previewText: '这是字号预览文本 - Font Size Preview Text'
    };
  }

  /**
   * 初始化后钩子
   */
  async afterInit() {
    // 获取当前字号
    const currentFontSize = document.documentElement.getAttribute('data-font-size') || 'md';
    this.setFontSize(currentFontSize, false);
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (!this.element) return;
    
    // 滑块变化事件
    const slider = this.element.querySelector('.font-size-slider');
    if (slider) {
      slider.addEventListener('input', (e) => {
        const index = parseInt(e.target.value);
        this.adjustFontSize(index);
      });
    }
    
    // 按钮点击事件
    this.element.addEventListener('click', (e) => {
      if (e.target.classList.contains('font-size-decrease')) {
        this.decreaseFontSize();
      } else if (e.target.classList.contains('font-size-increase')) {
        this.increaseFontSize();
      } else if (e.target.classList.contains('font-size-reset')) {
        this.resetFontSize();
      }
    });
    
    // 键盘事件
    this.element.addEventListener('keydown', (e) => {
      if (e.target.classList.contains('font-size-slider')) {
        // 阻止默认行为，使用自定义步进
        if (e.key === 'ArrowLeft' || e.key === 'ArrowDown') {
          e.preventDefault();
          this.decreaseFontSize();
        } else if (e.key === 'ArrowRight' || e.key === 'ArrowUp') {
          e.preventDefault();
          this.increaseFontSize();
        }
      }
    });
  }

  /**
   * 执行渲染
   */
  async doRender() {
    if (!this.element) return;
    
    const currentFont = this.fontSizes[this.currentIndex];
    
    this.element.innerHTML = `
      <div class="font-size-adjuster-header">
        <h3 class="font-size-adjuster-title">字体大小</h3>
        <p class="font-size-adjuster-description">调整界面文字的显示大小</p>
      </div>
      
      <div class="font-size-controls">
        <button class="btn btn-icon font-size-decrease" title="减小字号" ${this.currentIndex === 0 ? 'disabled' : ''}>
          <span>A-</span>
        </button>
        
        <div class="font-size-slider-container">
          <input type="range" 
                 class="font-size-slider" 
                 min="0" 
                 max="${this.fontSizes.length - 1}" 
                 value="${this.currentIndex}"
                 step="1">
          <div class="font-size-marks">
            ${this.fontSizes.map((_, index) => `
              <div class="font-size-mark ${index === this.currentIndex ? 'active' : ''}" 
                   style="left: ${(index / (this.fontSizes.length - 1)) * 100}%">
              </div>
            `).join('')}
          </div>
        </div>
        
        <button class="btn btn-icon font-size-increase" title="增大字号" ${this.currentIndex === this.fontSizes.length - 1 ? 'disabled' : ''}>
          <span>A+</span>
        </button>
      </div>
      
      <div class="font-size-info">
        <div class="current-font-info">
          <span class="current-font-name">${currentFont.name}</span>
          <span class="current-font-size">(${currentFont.size})</span>
        </div>
        <div class="current-font-description">${currentFont.description}</div>
      </div>
      
      <div class="font-size-preview">
        <div class="preview-label">预览效果：</div>
        <div class="preview-text" style="font-size: ${currentFont.size};">
          ${this.state.previewText}
        </div>
      </div>
      
      <div class="font-size-actions">
        <button class="btn btn-secondary font-size-reset">重置为默认</button>
      </div>
    `;
    
    this.updateSlider();
  }

  /**
   * 设置字号
   * @param {string} fontSizeId - 字号ID
   * @param {boolean} apply - 是否应用到文档
   */
  setFontSize(fontSizeId, apply = true) {
    const index = this.fontSizes.findIndex(f => f.id === fontSizeId);
    if (index === -1) return;
    
    this.currentFontSize = fontSizeId;
    this.currentIndex = index;
    
    this.setState({
      fontSize: fontSizeId,
      fontIndex: index
    });
    
    if (apply) {
      // 应用到文档
      document.documentElement.setAttribute('data-font-size', fontSizeId);
      
      // 触发事件
      this.emit('fontSizeChanged', {
        fontSizeId,
        fontSize: this.fontSizes[index]
      });
    }
    
    // 更新UI
    if (this.isInitialized) {
      this.render();
    }
  }

  /**
   * 调整字号
   * @param {number} index - 字号索引
   */
  adjustFontSize(index) {
    if (index < 0 || index >= this.fontSizes.length) return;
    
    const fontSizeId = this.fontSizes[index].id;
    this.setFontSize(fontSizeId);
  }

  /**
   * 减小字号
   */
  decreaseFontSize() {
    if (this.currentIndex > 0) {
      this.adjustFontSize(this.currentIndex - 1);
    }
  }

  /**
   * 增大字号
   */
  increaseFontSize() {
    if (this.currentIndex < this.fontSizes.length - 1) {
      this.adjustFontSize(this.currentIndex + 1);
    }
  }

  /**
   * 重置字号
   */
  resetFontSize() {
    this.setFontSize('md');
  }

  /**
   * 更新滑块状态
   */
  updateSlider() {
    if (!this.element) return;
    
    const slider = this.element.querySelector('.font-size-slider');
    if (slider) {
      slider.value = this.currentIndex;
    }
    
    // 更新标记点
    const marks = this.element.querySelectorAll('.font-size-mark');
    marks.forEach((mark, index) => {
      mark.classList.toggle('active', index === this.currentIndex);
    });
    
    // 更新按钮状态
    const decreaseBtn = this.element.querySelector('.font-size-decrease');
    const increaseBtn = this.element.querySelector('.font-size-increase');
    
    if (decreaseBtn) {
      decreaseBtn.disabled = this.currentIndex === 0;
    }
    
    if (increaseBtn) {
      increaseBtn.disabled = this.currentIndex === this.fontSizes.length - 1;
    }
  }

  /**
   * 获取当前字号
   * @returns {string} 字号ID
   */
  getCurrentFontSize() {
    return this.currentFontSize;
  }

  /**
   * 获取字号信息
   * @param {string} fontSizeId - 字号ID
   * @returns {object|null} 字号信息
   */
  getFontSizeInfo(fontSizeId) {
    return this.fontSizes.find(f => f.id === fontSizeId) || null;
  }

  /**
   * 设置预览文本
   * @param {string} text - 预览文本
   */
  setPreviewText(text) {
    this.setState('previewText', text);
    
    const previewElement = this.element?.querySelector('.preview-text');
    if (previewElement) {
      previewElement.textContent = text;
    }
  }
}

export default FontSizeAdjusterComponent;
