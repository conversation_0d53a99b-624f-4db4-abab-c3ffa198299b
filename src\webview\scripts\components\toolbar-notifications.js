/**
 * 工具栏通知集成组件
 * 为工具栏操作提供专门的通知反馈
 */

import { globalNotificationManager, NotificationTypes } from './notification-manager.js';
import { EventManager } from '../utils/events.js';

/**
 * 工具栏操作类型
 */
export const ToolbarActions = {
  COPY: 'copy',
  EDIT: 'edit',
  DELETE: 'delete',
  SAVE: 'save',
  CANCEL: 'cancel',
  EXPORT: 'export',
  IMPORT: 'import',
  RESET: 'reset',
  SEARCH: 'search',
  FILTER: 'filter',
  SETTINGS: 'settings'
};

/**
 * 工具栏通知管理器类
 */
export class ToolbarNotifications {
  constructor() {
    this.eventManager = new EventManager();
    this.notificationHistory = [];
    this.maxHistory = 50;
    
    // 操作消息模板
    this.messageTemplates = {
      [ToolbarActions.COPY]: {
        success: '已复制到剪贴板',
        error: '复制失败',
        loading: '正在复制...'
      },
      [ToolbarActions.EDIT]: {
        success: '编辑已保存',
        error: '编辑失败',
        loading: '正在保存编辑...',
        info: '进入编辑模式'
      },
      [ToolbarActions.DELETE]: {
        success: '删除成功',
        error: '删除失败',
        loading: '正在删除...',
        warning: '确定要删除吗？'
      },
      [ToolbarActions.SAVE]: {
        success: '保存成功',
        error: '保存失败',
        loading: '正在保存...'
      },
      [ToolbarActions.CANCEL]: {
        info: '操作已取消'
      },
      [ToolbarActions.EXPORT]: {
        success: '导出成功',
        error: '导出失败',
        loading: '正在导出...'
      },
      [ToolbarActions.IMPORT]: {
        success: '导入成功',
        error: '导入失败',
        loading: '正在导入...'
      },
      [ToolbarActions.RESET]: {
        success: '重置成功',
        warning: '确定要重置吗？',
        loading: '正在重置...'
      },
      [ToolbarActions.SEARCH]: {
        success: '搜索完成',
        info: '未找到匹配结果',
        loading: '正在搜索...'
      },
      [ToolbarActions.FILTER]: {
        success: '筛选已应用',
        info: '筛选已清除'
      },
      [ToolbarActions.SETTINGS]: {
        success: '设置已保存',
        error: '设置保存失败',
        info: '设置已重置'
      }
    };
    
    // 绑定方法上下文
    this.notify = this.notify.bind(this);
    this.notifySuccess = this.notifySuccess.bind(this);
    this.notifyError = this.notifyError.bind(this);
    this.notifyWarning = this.notifyWarning.bind(this);
    this.notifyInfo = this.notifyInfo.bind(this);
    this.notifyLoading = this.notifyLoading.bind(this);
  }

  /**
   * 通用通知方法
   * @param {string} action - 操作类型
   * @param {string} type - 通知类型
   * @param {string} customMessage - 自定义消息
   * @param {object} options - 选项
   * @returns {string} 通知ID
   */
  notify(action, type, customMessage = null, options = {}) {
    const template = this.messageTemplates[action];
    const message = customMessage || (template && template[type]) || '操作完成';
    
    const notificationOptions = {
      duration: this.getDefaultDuration(type),
      groupKey: `toolbar-${action}`,
      ...options
    };
    
    // 记录到历史
    this.addToHistory(action, type, message);
    
    // 显示通知
    const notificationId = globalNotificationManager.show(message, type, notificationOptions);
    
    // 触发事件
    this.eventManager.emit('toolbarNotification', {
      action,
      type,
      message,
      notificationId
    });
    
    return notificationId;
  }

  /**
   * 成功通知
   * @param {string} action - 操作类型
   * @param {string} customMessage - 自定义消息
   * @param {object} options - 选项
   * @returns {string} 通知ID
   */
  notifySuccess(action, customMessage = null, options = {}) {
    return this.notify(action, NotificationTypes.SUCCESS, customMessage, options);
  }

  /**
   * 错误通知
   * @param {string} action - 操作类型
   * @param {string} customMessage - 自定义消息
   * @param {object} options - 选项
   * @returns {string} 通知ID
   */
  notifyError(action, customMessage = null, options = {}) {
    return this.notify(action, NotificationTypes.ERROR, customMessage, {
      duration: 5000, // 错误通知显示更长时间
      ...options
    });
  }

  /**
   * 警告通知
   * @param {string} action - 操作类型
   * @param {string} customMessage - 自定义消息
   * @param {object} options - 选项
   * @returns {string} 通知ID
   */
  notifyWarning(action, customMessage = null, options = {}) {
    return this.notify(action, NotificationTypes.WARNING, customMessage, options);
  }

  /**
   * 信息通知
   * @param {string} action - 操作类型
   * @param {string} customMessage - 自定义消息
   * @param {object} options - 选项
   * @returns {string} 通知ID
   */
  notifyInfo(action, customMessage = null, options = {}) {
    return this.notify(action, NotificationTypes.INFO, customMessage, options);
  }

  /**
   * 加载通知
   * @param {string} action - 操作类型
   * @param {string} customMessage - 自定义消息
   * @param {object} options - 选项
   * @returns {string} 通知ID
   */
  notifyLoading(action, customMessage = null, options = {}) {
    return this.notify(action, NotificationTypes.LOADING, customMessage, {
      persistent: true, // 加载通知需要手动关闭
      closable: false,
      showProgress: false,
      ...options
    });
  }

  /**
   * 确认操作通知
   * @param {string} action - 操作类型
   * @param {function} onConfirm - 确认回调
   * @param {function} onCancel - 取消回调
   * @param {string} customMessage - 自定义消息
   * @returns {string} 通知ID
   */
  notifyConfirm(action, onConfirm, onCancel = null, customMessage = null) {
    const template = this.messageTemplates[action];
    const message = customMessage || (template && template.warning) || '确定要执行此操作吗？';
    
    return globalNotificationManager.show(message, NotificationTypes.WARNING, {
      duration: 0, // 不自动关闭
      persistent: true,
      actions: [
        {
          label: '确定',
          handler: () => {
            if (onConfirm) onConfirm();
          },
          closeOnClick: true
        },
        {
          label: '取消',
          handler: () => {
            if (onCancel) onCancel();
          },
          closeOnClick: true
        }
      ],
      groupKey: `toolbar-confirm-${action}`
    });
  }

  /**
   * 进度通知
   * @param {string} action - 操作类型
   * @param {number} progress - 进度百分比 (0-100)
   * @param {string} customMessage - 自定义消息
   * @returns {string} 通知ID
   */
  notifyProgress(action, progress, customMessage = null) {
    const template = this.messageTemplates[action];
    const baseMessage = customMessage || (template && template.loading) || '正在处理...';
    const message = `${baseMessage} (${Math.round(progress)}%)`;
    
    return globalNotificationManager.show(message, NotificationTypes.LOADING, {
      persistent: true,
      closable: false,
      showProgress: true,
      groupKey: `toolbar-progress-${action}`,
      progress
    });
  }

  /**
   * 批量操作通知
   * @param {string} action - 操作类型
   * @param {number} total - 总数
   * @param {number} completed - 已完成数
   * @param {number} failed - 失败数
   * @returns {string} 通知ID
   */
  notifyBatch(action, total, completed, failed = 0) {
    const remaining = total - completed - failed;
    let message, type;
    
    if (remaining > 0) {
      // 进行中
      message = `批量${action}进行中... (${completed}/${total})`;
      type = NotificationTypes.LOADING;
    } else if (failed === 0) {
      // 全部成功
      message = `批量${action}完成 (${completed}/${total})`;
      type = NotificationTypes.SUCCESS;
    } else if (completed === 0) {
      // 全部失败
      message = `批量${action}失败 (${failed}/${total})`;
      type = NotificationTypes.ERROR;
    } else {
      // 部分成功
      message = `批量${action}完成 (成功: ${completed}, 失败: ${failed})`;
      type = NotificationTypes.WARNING;
    }
    
    return globalNotificationManager.show(message, type, {
      persistent: remaining > 0,
      closable: remaining === 0,
      groupKey: `toolbar-batch-${action}`
    });
  }

  /**
   * 获取默认持续时间
   * @param {string} type - 通知类型
   * @returns {number} 持续时间（毫秒）
   */
  getDefaultDuration(type) {
    const durations = {
      [NotificationTypes.SUCCESS]: 3000,
      [NotificationTypes.INFO]: 4000,
      [NotificationTypes.WARNING]: 5000,
      [NotificationTypes.ERROR]: 6000,
      [NotificationTypes.LOADING]: 0 // 不自动关闭
    };
    
    return durations[type] || 4000;
  }

  /**
   * 添加到历史记录
   * @param {string} action - 操作类型
   * @param {string} type - 通知类型
   * @param {string} message - 消息内容
   */
  addToHistory(action, type, message) {
    const historyItem = {
      action,
      type,
      message,
      timestamp: Date.now()
    };
    
    this.notificationHistory.unshift(historyItem);
    
    // 限制历史记录数量
    if (this.notificationHistory.length > this.maxHistory) {
      this.notificationHistory = this.notificationHistory.slice(0, this.maxHistory);
    }
  }

  /**
   * 获取通知历史
   * @param {number} limit - 限制数量
   * @returns {Array} 历史记录
   */
  getHistory(limit = 10) {
    return this.notificationHistory.slice(0, limit);
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.notificationHistory = [];
    this.eventManager.emit('historyCleared');
  }

  /**
   * 获取统计信息
   * @returns {object} 统计信息
   */
  getStatistics() {
    const stats = {
      total: this.notificationHistory.length,
      byAction: {},
      byType: {}
    };
    
    this.notificationHistory.forEach(item => {
      stats.byAction[item.action] = (stats.byAction[item.action] || 0) + 1;
      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;
    });
    
    return stats;
  }

  /**
   * 获取事件管理器
   * @returns {EventManager} 事件管理器
   */
  getEventManager() {
    return this.eventManager;
  }
}

// 创建全局实例
export const globalToolbarNotifications = new ToolbarNotifications();

export default ToolbarNotifications;
