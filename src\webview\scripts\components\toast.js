/**
 * Toast通知组件
 * 提供各种类型的通知消息显示
 */

import BaseComponent from './base.js';
import { DOMUtils } from '../utils/dom.js';

/**
 * Toast组件类
 */
export class ToastComponent extends BaseComponent {
  constructor() {
    const toastContainer = document.getElementById('toastContainer');
    super(toastContainer, {
      className: 'toast-component',
      autoInit: true
    });
    
    this.toasts = new Map();
    this.maxToasts = 5;
    this.defaultDuration = 3000;
    
    // 绑定方法上下文
    this.show = this.show.bind(this);
    this.hide = this.hide.bind(this);
    this.clear = this.clear.bind(this);
  }

  /**
   * 获取初始状态
   */
  getInitialState() {
    return {
      ...super.getInitialState(),
      toastCount: 0
    };
  }

  /**
   * 显示Toast通知
   * @param {string} message - 消息内容
   * @param {string} type - 通知类型 (success, warning, error, info)
   * @param {object} options - 选项
   * @returns {string} Toast ID
   */
  show(message, type = 'info', options = {}) {
    const toastOptions = {
      duration: this.defaultDuration,
      closable: true,
      persistent: false,
      ...options
    };
    
    // 检查Toast数量限制
    if (this.toasts.size >= this.maxToasts) {
      this.removeOldestToast();
    }
    
    const toastId = this.generateToastId();
    const toastElement = this.createToastElement(toastId, message, type, toastOptions);
    
    // 添加到容器
    if (this.element) {
      this.element.appendChild(toastElement);
    }
    
    // 存储Toast信息
    this.toasts.set(toastId, {
      element: toastElement,
      type,
      message,
      options: toastOptions,
      timestamp: Date.now()
    });
    
    // 显示动画
    requestAnimationFrame(() => {
      DOMUtils.addClass(toastElement, 'show');
    });
    
    // 设置自动隐藏
    if (!toastOptions.persistent && toastOptions.duration > 0) {
      setTimeout(() => {
        this.hide(toastId);
      }, toastOptions.duration);
    }
    
    this.setState('toastCount', this.toasts.size);
    this.emit('toastShown', { id: toastId, message, type });
    
    return toastId;
  }

  /**
   * 创建Toast元素
   * @param {string} id - Toast ID
   * @param {string} message - 消息内容
   * @param {string} type - 通知类型
   * @param {object} options - 选项
   * @returns {Element} Toast元素
   */
  createToastElement(id, message, type, options) {
    const toast = DOMUtils.createElement('div', {
      className: `toast toast-${type}`,
      'data-toast-id': id
    });
    
    // Toast头部
    const header = DOMUtils.createElement('div', {
      className: 'toast-header'
    });
    
    // 标题
    const title = DOMUtils.createElement('span', {
      className: 'toast-title',
      textContent: this.getTypeTitle(type)
    });
    header.appendChild(title);
    
    // 关闭按钮
    if (options.closable) {
      const closeBtn = DOMUtils.createElement('button', {
        className: 'toast-close',
        innerHTML: '×',
        onclick: () => this.hide(id)
      });
      header.appendChild(closeBtn);
    }
    
    toast.appendChild(header);
    
    // Toast内容
    const body = DOMUtils.createElement('div', {
      className: 'toast-body'
    });
    
    if (typeof message === 'string') {
      body.textContent = message;
    } else {
      body.appendChild(message);
    }
    
    toast.appendChild(body);
    
    // 进度条（如果有持续时间）
    if (!options.persistent && options.duration > 0) {
      const progress = DOMUtils.createElement('div', {
        className: 'toast-progress'
      });
      
      const progressBar = DOMUtils.createElement('div', {
        className: 'toast-progress-bar'
      });
      
      progress.appendChild(progressBar);
      toast.appendChild(progress);
      
      // 动画进度条
      requestAnimationFrame(() => {
        progressBar.style.transition = `width ${options.duration}ms linear`;
        progressBar.style.width = '0%';
      });
    }
    
    return toast;
  }

  /**
   * 获取类型标题
   * @param {string} type - 通知类型
   * @returns {string} 标题
   */
  getTypeTitle(type) {
    const titles = {
      success: '成功',
      warning: '警告',
      error: '错误',
      info: '信息'
    };
    return titles[type] || '通知';
  }

  /**
   * 隐藏Toast
   * @param {string} toastId - Toast ID
   */
  hide(toastId) {
    const toast = this.toasts.get(toastId);
    if (!toast) return;
    
    const { element } = toast;
    
    // 隐藏动画
    DOMUtils.removeClass(element, 'show');
    
    // 移除元素
    setTimeout(() => {
      if (element.parentElement) {
        element.parentElement.removeChild(element);
      }
      this.toasts.delete(toastId);
      this.setState('toastCount', this.toasts.size);
      this.emit('toastHidden', { id: toastId });
    }, 300);
  }

  /**
   * 移除最旧的Toast
   */
  removeOldestToast() {
    let oldestId = null;
    let oldestTimestamp = Infinity;
    
    for (const [id, toast] of this.toasts) {
      if (toast.timestamp < oldestTimestamp) {
        oldestTimestamp = toast.timestamp;
        oldestId = id;
      }
    }
    
    if (oldestId) {
      this.hide(oldestId);
    }
  }

  /**
   * 清空所有Toast
   */
  clear() {
    const toastIds = Array.from(this.toasts.keys());
    toastIds.forEach(id => this.hide(id));
  }

  /**
   * 显示成功通知
   * @param {string} message - 消息内容
   * @param {object} options - 选项
   * @returns {string} Toast ID
   */
  success(message, options = {}) {
    return this.show(message, 'success', options);
  }

  /**
   * 显示警告通知
   * @param {string} message - 消息内容
   * @param {object} options - 选项
   * @returns {string} Toast ID
   */
  warning(message, options = {}) {
    return this.show(message, 'warning', options);
  }

  /**
   * 显示错误通知
   * @param {string} message - 消息内容
   * @param {object} options - 选项
   * @returns {string} Toast ID
   */
  error(message, options = {}) {
    return this.show(message, 'error', {
      duration: 5000, // 错误消息显示更长时间
      ...options
    });
  }

  /**
   * 显示信息通知
   * @param {string} message - 消息内容
   * @param {object} options - 选项
   * @returns {string} Toast ID
   */
  info(message, options = {}) {
    return this.show(message, 'info', options);
  }

  /**
   * 显示加载通知
   * @param {string} message - 消息内容
   * @param {object} options - 选项
   * @returns {string} Toast ID
   */
  loading(message = '加载中...', options = {}) {
    const loadingElement = DOMUtils.createElement('div', {
      className: 'flex items-center gap-sm'
    });
    
    const spinner = DOMUtils.createElement('div', {
      className: 'spinner'
    });
    
    const text = DOMUtils.createElement('span', {
      textContent: message
    });
    
    loadingElement.appendChild(spinner);
    loadingElement.appendChild(text);
    
    return this.show(loadingElement, 'info', {
      persistent: true,
      closable: false,
      ...options
    });
  }

  /**
   * 更新Toast内容
   * @param {string} toastId - Toast ID
   * @param {string} message - 新消息内容
   * @param {string} type - 新类型（可选）
   */
  update(toastId, message, type = null) {
    const toast = this.toasts.get(toastId);
    if (!toast) return;
    
    const { element } = toast;
    const body = element.querySelector('.toast-body');
    
    if (body) {
      if (typeof message === 'string') {
        body.textContent = message;
      } else {
        body.innerHTML = '';
        body.appendChild(message);
      }
    }
    
    // 更新类型
    if (type && type !== toast.type) {
      element.className = element.className.replace(`toast-${toast.type}`, `toast-${type}`);
      
      const title = element.querySelector('.toast-title');
      if (title) {
        title.textContent = this.getTypeTitle(type);
      }
      
      toast.type = type;
    }
    
    toast.message = message;
    this.emit('toastUpdated', { id: toastId, message, type });
  }

  /**
   * 获取Toast信息
   * @param {string} toastId - Toast ID
   * @returns {object|null} Toast信息
   */
  getToast(toastId) {
    return this.toasts.get(toastId) || null;
  }

  /**
   * 获取所有Toast
   * @returns {Array} Toast列表
   */
  getAllToasts() {
    return Array.from(this.toasts.entries()).map(([id, toast]) => ({
      id,
      ...toast
    }));
  }

  /**
   * 设置最大Toast数量
   * @param {number} max - 最大数量
   */
  setMaxToasts(max) {
    this.maxToasts = Math.max(1, max);
    
    // 如果当前Toast数量超过限制，移除多余的
    while (this.toasts.size > this.maxToasts) {
      this.removeOldestToast();
    }
  }

  /**
   * 设置默认持续时间
   * @param {number} duration - 持续时间（毫秒）
   */
  setDefaultDuration(duration) {
    this.defaultDuration = Math.max(0, duration);
  }

  /**
   * 生成Toast ID
   * @returns {string} Toast ID
   */
  generateToastId() {
    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁前钩子
   */
  beforeDestroy() {
    this.clear();
  }
}

export default ToastComponent;
