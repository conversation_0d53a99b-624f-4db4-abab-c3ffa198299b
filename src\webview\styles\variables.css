/* CSS变量系统 - 统一的设计令牌 */

:root {
  /* 颜色系统 - 基于VS Code主题变量 */
  --color-primary: var(--vscode-button-background);
  --color-primary-hover: var(--vscode-button-hoverBackground);
  --color-primary-foreground: var(--vscode-button-foreground);
  
  --color-secondary: var(--vscode-button-secondaryBackground);
  --color-secondary-hover: var(--vscode-button-secondaryHoverBackground);
  --color-secondary-foreground: var(--vscode-button-secondaryForeground);
  
  --color-background: var(--vscode-editor-background);
  --color-surface: var(--vscode-sideBar-background);
  --color-surface-hover: var(--vscode-list-hoverBackground);
  --color-surface-active: var(--vscode-list-activeSelectionBackground);
  
  --color-foreground: var(--vscode-foreground);
  --color-foreground-muted: var(--vscode-descriptionForeground);
  --color-foreground-active: var(--vscode-list-activeSelectionForeground);
  
  --color-border: var(--vscode-widget-border);
  --color-border-focus: var(--vscode-focusBorder);
  --color-border-input: var(--vscode-input-border);
  
  --color-success: var(--vscode-testing-iconPassed, #89d185);
  --color-warning: var(--vscode-testing-iconQueued, #ffcc02);
  --color-error: var(--vscode-testing-iconFailed, #f85149);
  --color-info: var(--vscode-testing-iconUnset, #58a6ff);

  /* 扩展的VS Code主题变量 */
  --color-input-background: var(--vscode-input-background, var(--color-surface));
  --color-input-foreground: var(--vscode-input-foreground, var(--color-foreground));
  --color-input-placeholder: var(--vscode-input-placeholderForeground, var(--color-foreground-muted));

  --color-dropdown-background: var(--vscode-dropdown-background, var(--color-surface));
  --color-dropdown-foreground: var(--vscode-dropdown-foreground, var(--color-foreground));
  --color-dropdown-border: var(--vscode-dropdown-border, var(--color-border));

  --color-list-hover: var(--vscode-list-hoverBackground, var(--color-surface-hover));
  --color-list-active: var(--vscode-list-activeSelectionBackground, var(--color-surface-active));
  --color-list-focus: var(--vscode-list-focusBackground, var(--color-surface-hover));

  --color-editor-selection: var(--vscode-editor-selectionBackground, rgba(0, 120, 212, 0.3));
  --color-editor-line-highlight: var(--vscode-editor-lineHighlightBackground, transparent);

  /* 颜色RGB值（用于透明度计算） */
  --color-primary-rgb: 0, 120, 212;
  --color-success-rgb: 137, 209, 133;
  --color-warning-rgb: 255, 204, 2;
  --color-error-rgb: 248, 81, 73;
  --color-info-rgb: 88, 166, 255;
  
  /* 字体系统 */
  --font-family: var(--vscode-font-family);
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: var(--vscode-font-size, 13px);
  --font-size-lg: 14px;
  --font-size-xl: 16px;
  --font-size-2xl: 18px;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* 间距系统 */
  --spacing-xs: 2px;
  --spacing-sm: 4px;
  --spacing-md: 8px;
  --spacing-lg: 12px;
  --spacing-xl: 16px;
  --spacing-2xl: 20px;
  --spacing-3xl: 24px;
  --spacing-4xl: 32px;
  
  /* 圆角系统 */
  --radius-none: 0;
  --radius-sm: 2px;
  --radius-md: 3px;
  --radius-lg: 4px;
  --radius-xl: 6px;
  --radius-full: 9999px;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --transition-very-slow: 500ms ease-in-out;

  /* 缓动函数 */
  --easing-ease: ease;
  --easing-ease-in: ease-in;
  --easing-ease-out: ease-out;
  --easing-ease-in-out: ease-in-out;
  --easing-linear: linear;
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --easing-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --easing-sharp: cubic-bezier(0.4, 0.0, 0.6, 1);

  /* 动画持续时间 */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-very-slow: 500ms;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* 布局系统 */
  --container-max-width: 100%;
  --content-max-width: 1200px;
  --content-optimal-width: 800px;
  --sidebar-width: 300px;

  /* 消息系统特定变量 */
  --message-container-max-width: 1000px;
  --message-max-width: 85%;
  --message-user-max-width: 75%;
  --message-ai-max-width: 90%;
  --message-padding: var(--spacing-lg);
  --message-border-radius: var(--radius-md);
  --message-gap: var(--spacing-md);
  
  /* 输入框系统 */
  --input-padding-x: var(--spacing-lg);
  --input-padding-y: var(--spacing-md);
  --input-border-radius: var(--radius-sm);
  --input-min-height: 80px;
  --input-max-height: 50vh;
  
  /* 按钮系统 */
  --button-padding-x: var(--spacing-lg);
  --button-padding-y: var(--spacing-md);
  --button-border-radius: var(--radius-sm);
  --button-min-height: 32px;
  
  /* 通知系统 */
  --toast-width: 320px;
  --toast-padding: var(--spacing-lg);
  --toast-border-radius: var(--radius-md);
  --toast-offset: var(--spacing-xl);

  /* 响应式网格系统 */
  --grid-columns: 12;
  --grid-gap: var(--spacing-md);
  --grid-margin: var(--spacing-lg);

  /* 断点系统 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* 容器最大宽度 */
  --container-xs: 100%;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* 主题适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  }
}

/* 用户自定义字号支持 */
[data-font-size="xs"] {
  --font-size-base: 11px;
}

[data-font-size="sm"] {
  --font-size-base: 12px;
}

[data-font-size="md"] {
  --font-size-base: 13px;
}

[data-font-size="lg"] {
  --font-size-base: 14px;
}

[data-font-size="xl"] {
  --font-size-base: 16px;
}

[data-font-size="2xl"] {
  --font-size-base: 18px;
}
