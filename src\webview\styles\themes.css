/* 主题系统 */

/* 默认主题 - 跟随VS Code */
[data-theme="auto"] {
  /* 使用CSS变量，自动适配VS Code主题 */

  /* 确保颜色RGB值在自动模式下正确设置 */
  --color-primary-rgb: 0, 120, 212;
  --color-success-rgb: 137, 209, 133;
  --color-warning-rgb: 255, 204, 2;
  --color-error-rgb: 248, 81, 73;
  --color-info-rgb: 88, 166, 255;
}

/* 自动检测系统主题偏好 */
@media (prefers-color-scheme: dark) {
  [data-theme="auto"] {
    --color-primary-rgb: 0, 120, 212;
    --color-success-rgb: 137, 209, 133;
    --color-warning-rgb: 255, 204, 2;
    --color-error-rgb: 248, 81, 73;
    --color-info-rgb: 88, 166, 255;
  }
}

@media (prefers-color-scheme: light) {
  [data-theme="auto"] {
    --color-primary-rgb: 0, 120, 212;
    --color-success-rgb: 16, 124, 16;
    --color-warning-rgb: 255, 140, 0;
    --color-error-rgb: 209, 52, 56;
    --color-info-rgb: 0, 120, 212;
  }
}

/* 浅色主题 */
[data-theme="light"] {
  --color-primary: #0078d4;
  --color-primary-hover: #106ebe;
  --color-primary-foreground: #ffffff;
  
  --color-secondary: #f3f2f1;
  --color-secondary-hover: #edebe9;
  --color-secondary-foreground: #323130;
  
  --color-background: #ffffff;
  --color-surface: #f8f8f8;
  --color-surface-hover: #f0f0f0;
  --color-surface-active: #0078d4;
  
  --color-foreground: #323130;
  --color-foreground-muted: #605e5c;
  --color-foreground-active: #ffffff;
  
  --color-border: #d1d1d1;
  --color-border-focus: #0078d4;
  --color-border-input: #d1d1d1;
  
  --color-success: #107c10;
  --color-warning: #ff8c00;
  --color-error: #d13438;
  --color-info: #0078d4;
  
  /* 浅色主题特定阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 深色主题 */
[data-theme="dark"] {
  --color-primary: #0078d4;
  --color-primary-hover: #1a86d9;
  --color-primary-foreground: #ffffff;
  
  --color-secondary: #2d2d30;
  --color-secondary-hover: #3e3e42;
  --color-secondary-foreground: #cccccc;
  
  --color-background: #1e1e1e;
  --color-surface: #252526;
  --color-surface-hover: #2a2d2e;
  --color-surface-active: #094771;
  
  --color-foreground: #cccccc;
  --color-foreground-muted: #969696;
  --color-foreground-active: #ffffff;
  
  --color-border: #3e3e42;
  --color-border-focus: #0078d4;
  --color-border-input: #3e3e42;
  
  --color-success: #89d185;
  --color-warning: #ffcc02;
  --color-error: #f85149;
  --color-info: #58a6ff;
  
  /* 深色主题特定阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
}

/* 高对比度主题 */
[data-theme="high-contrast"] {
  --color-primary: #ffffff;
  --color-primary-hover: #ffffff;
  --color-primary-foreground: #000000;
  
  --color-secondary: #000000;
  --color-secondary-hover: #000000;
  --color-secondary-foreground: #ffffff;
  
  --color-background: #000000;
  --color-surface: #000000;
  --color-surface-hover: #000000;
  --color-surface-active: #ffffff;
  
  --color-foreground: #ffffff;
  --color-foreground-muted: #ffffff;
  --color-foreground-active: #000000;
  
  --color-border: #ffffff;
  --color-border-focus: #ffffff;
  --color-border-input: #ffffff;
  
  --color-success: #00ff00;
  --color-warning: #ffff00;
  --color-error: #ff0000;
  --color-info: #00ffff;
  
  /* 高对比度主题特定样式 */
  --shadow-sm: none;
  --shadow-md: none;
  --shadow-lg: none;
}

/* 主题特定组件样式 */

/* 浅色主题消息样式 */
[data-theme="light"] .message-user .message-content {
  background-color: #0078d4;
  color: #ffffff;
  border-color: #0078d4;
}

[data-theme="light"] .message-ai .message-content {
  background-color: #f0f0f0;
  color: #323130;
  border-color: #d1d1d1;
}

/* 深色主题消息样式 */
[data-theme="dark"] .message-user .message-content {
  background-color: #094771;
  color: #ffffff;
  border-color: #0078d4;
}

[data-theme="dark"] .message-ai .message-content {
  background-color: #2a2d2e;
  color: #cccccc;
  border-color: #3e3e42;
}

/* 高对比度主题消息样式 */
[data-theme="high-contrast"] .message-user .message-content {
  background-color: #ffffff;
  color: #000000;
  border: 2px solid #ffffff;
}

[data-theme="high-contrast"] .message-ai .message-content {
  background-color: #000000;
  color: #ffffff;
  border: 2px solid #ffffff;
}

/* 主题切换动画 */
* {
  transition: background-color var(--transition-fast),
              color var(--transition-fast),
              border-color var(--transition-fast);
}

/* 禁用主题切换动画（性能优化） */
.theme-switching * {
  transition: none !important;
}

/* 主题特定的滚动条样式 */
[data-theme="light"] .messages-container::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
}

[data-theme="light"] .messages-container::-webkit-scrollbar-thumb:hover {
  background-color: #b3b3b3;
}

[data-theme="dark"] .messages-container::-webkit-scrollbar-thumb {
  background-color: #3e3e42;
}

[data-theme="dark"] .messages-container::-webkit-scrollbar-thumb:hover {
  background-color: #4e4e52;
}

[data-theme="high-contrast"] .messages-container::-webkit-scrollbar-thumb {
  background-color: #ffffff;
}

/* 主题特定的Toast样式 */
[data-theme="light"] .toast {
  background-color: #ffffff;
  border-color: #d1d1d1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .toast {
  background-color: #2d2d30;
  border-color: #3e3e42;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

[data-theme="high-contrast"] .toast {
  background-color: #000000;
  border: 2px solid #ffffff;
  box-shadow: none;
}

/* 主题特定的输入框样式 */
[data-theme="light"] .input {
  background-color: #ffffff;
  border-color: #d1d1d1;
  color: #323130;
}

[data-theme="dark"] .input {
  background-color: #1e1e1e;
  border-color: #3e3e42;
  color: #cccccc;
}

[data-theme="high-contrast"] .input {
  background-color: #000000;
  border: 2px solid #ffffff;
  color: #ffffff;
}

/* 主题特定的按钮样式 */
[data-theme="high-contrast"] .btn {
  border-width: 2px;
}

[data-theme="high-contrast"] .btn-primary {
  background-color: #ffffff;
  color: #000000;
  border-color: #ffffff;
}

[data-theme="high-contrast"] .btn-secondary {
  background-color: #000000;
  color: #ffffff;
  border-color: #ffffff;
}

/* 系统主题检测 */
@media (prefers-color-scheme: dark) {
  [data-theme="auto"] {
    color-scheme: dark;
  }
}

@media (prefers-color-scheme: light) {
  [data-theme="auto"] {
    color-scheme: light;
  }
}

/* 常见VS Code主题特殊支持 */

/* One Dark Pro主题 */
body[data-vscode-theme-kind="vscode-dark"][data-vscode-theme-name*="One Dark"] {
  --color-primary: #61afef;
  --color-primary-rgb: 97, 175, 239;
  --color-success: #98c379;
  --color-success-rgb: 152, 195, 121;
  --color-warning: #e5c07b;
  --color-warning-rgb: 229, 192, 123;
  --color-error: #e06c75;
  --color-error-rgb: 224, 108, 117;
}

/* Dracula主题 */
body[data-vscode-theme-kind="vscode-dark"][data-vscode-theme-name*="Dracula"] {
  --color-primary: #bd93f9;
  --color-primary-rgb: 189, 147, 249;
  --color-success: #50fa7b;
  --color-success-rgb: 80, 250, 123;
  --color-warning: #f1fa8c;
  --color-warning-rgb: 241, 250, 140;
  --color-error: #ff5555;
  --color-error-rgb: 255, 85, 85;
}

/* Material Theme */
body[data-vscode-theme-kind="vscode-dark"][data-vscode-theme-name*="Material"] {
  --color-primary: #82aaff;
  --color-primary-rgb: 130, 170, 255;
  --color-success: #c3e88d;
  --color-success-rgb: 195, 232, 141;
  --color-warning: #ffcb6b;
  --color-warning-rgb: 255, 203, 107;
  --color-error: #f07178;
  --color-error-rgb: 240, 113, 120;
}

/* Monokai主题 */
body[data-vscode-theme-kind="vscode-dark"][data-vscode-theme-name*="Monokai"] {
  --color-primary: #66d9ef;
  --color-primary-rgb: 102, 217, 239;
  --color-success: #a6e22e;
  --color-success-rgb: 166, 226, 46;
  --color-warning: #e6db74;
  --color-warning-rgb: 230, 219, 116;
  --color-error: #f92672;
  --color-error-rgb: 249, 38, 114;
}

/* Solarized主题 */
body[data-vscode-theme-kind="vscode-light"][data-vscode-theme-name*="Solarized Light"] {
  --color-primary: #268bd2;
  --color-primary-rgb: 38, 139, 210;
  --color-success: #859900;
  --color-success-rgb: 133, 153, 0;
  --color-warning: #b58900;
  --color-warning-rgb: 181, 137, 0;
  --color-error: #dc322f;
  --color-error-rgb: 220, 50, 47;
}

body[data-vscode-theme-kind="vscode-dark"][data-vscode-theme-name*="Solarized Dark"] {
  --color-primary: #268bd2;
  --color-primary-rgb: 38, 139, 210;
  --color-success: #859900;
  --color-success-rgb: 133, 153, 0;
  --color-warning: #b58900;
  --color-warning-rgb: 181, 137, 0;
  --color-error: #dc322f;
  --color-error-rgb: 220, 50, 47;
}

/* 无障碍和对比度增强 */
@media (prefers-contrast: high) {
  :root {
    --color-border: currentColor;
    --shadow-sm: none;
    --shadow-md: none;
    --shadow-lg: none;
  }

  .message-content,
  .btn,
  .input,
  .toast {
    border-width: 2px;
    outline: 1px solid currentColor;
    outline-offset: 1px;
  }
}

/* 强制颜色模式支持 */
@media (forced-colors: active) {
  * {
    forced-color-adjust: auto;
  }

  .message-content,
  .btn,
  .input,
  .toast {
    forced-color-adjust: none;
    border: 1px solid ButtonText;
    background: ButtonFace;
    color: ButtonText;
  }

  .btn-primary {
    background: Highlight;
    color: HighlightText;
    border-color: Highlight;
  }

  .message-user .message-content {
    background: Highlight;
    color: HighlightText;
  }

  .message-ai .message-content {
    background: ButtonFace;
    color: ButtonText;
  }
}
