<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本管理测试</title>
    <link rel="stylesheet" href="../styles/variables.css">
    <link rel="stylesheet" href="../styles/components.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.success {
            background: #28a745;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .demo-message {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            background: #fafafa;
        }
        .demo-message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .demo-message-content {
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .demo-ai-reply {
            margin-left: 20px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 4px;
            border-left: 3px solid #007acc;
            margin-top: 10px;
        }
        .version-history {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .version-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .version-item.current {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .version-content {
            flex: 1;
            margin-right: 10px;
        }
        .version-meta {
            font-size: 12px;
            color: #666;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .version-actions {
            display: flex;
            gap: 5px;
        }
        .version-actions button {
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
        }
        .version-actions button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>版本管理测试</h1>
        <p>测试新的版本管理系统，包括版本切换、版本控件、版本历史等功能</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 版本控件测试</h3>
            <div class="controls">
                <button class="button" onclick="createSingleVersionMessage()">创建单版本消息</button>
                <button class="button" onclick="createMultiVersionMessage()">创建多版本消息</button>
                <button class="button" onclick="testVersionSwitching()">测试版本切换</button>
                <button class="button" onclick="testVersionControls()">测试版本控件</button>
            </div>
            <div id="versionControlsDemo" class="demo-message" style="display: none;">
                <div class="demo-message-header">
                    <span>演示消息</span>
                    <div id="demoVersionControls"></div>
                </div>
                <div id="demoMessageContent" class="demo-message-content"></div>
                <div id="demoAIReply" class="demo-ai-reply" style="display: none;"></div>
            </div>
            <div id="controlsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 版本操作测试</h3>
            <div class="controls">
                <button class="button" onclick="testJumpToVersion()">跳转到版本</button>
                <button class="button" onclick="testDuplicateVersion()">复制版本</button>
                <button class="button danger" onclick="testDeleteVersion()">删除版本</button>
                <button class="button" onclick="testVersionHistory()">查看版本历史</button>
            </div>
            <div id="operationsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. AI回复版本关联测试</h3>
            <div class="controls">
                <button class="button" onclick="testVersionWithAIReply()">版本+AI回复</button>
                <button class="button" onclick="testVersionSwitchWithAI()">切换版本+AI回复</button>
                <button class="button" onclick="testAIReplyStates()">AI回复状态</button>
            </div>
            <div id="aiReplyResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 版本历史可视化</h3>
            <div class="controls">
                <button class="button" onclick="showVersionHistory()">显示版本历史</button>
                <button class="button" onclick="updateVersionHistory()">更新历史</button>
                <button class="button" onclick="clearVersionHistory()">清空历史</button>
            </div>
            <div id="versionHistory" class="version-history" style="display: none;">
                <h4>版本历史</h4>
                <div id="versionHistoryContent"></div>
            </div>
            <div id="historyResult" class="result"></div>
        </div>
    </div>

    <script type="module">
        // 模拟版本管理器
        class VersionManager {
            constructor() {
                this.testMessage = null;
                this.eventListeners = new Map();
            }

            // 创建测试消息
            createTestMessage(content, versions = null) {
                const message = {
                    id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    isUser: true,
                    timestamp: Date.now(),
                    versions: versions || [{
                        id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                        content: content,
                        timestamp: Date.now(),
                        aiReply: null,
                        metadata: {}
                    }],
                    currentVersionIndex: 0,
                    metadata: {}
                };
                return message;
            }

            // 创建版本控件
            createVersionControls(message) {
                const versionControls = document.createElement('div');
                versionControls.className = 'message-version-controls';

                const prevBtn = document.createElement('button');
                prevBtn.className = 'btn btn-icon version-btn version-prev-btn';
                prevBtn.innerHTML = '◀';
                prevBtn.title = '上一个版本';

                const versionInfo = document.createElement('div');
                versionInfo.className = 'version-info';

                const versionCounter = document.createElement('span');
                versionCounter.className = 'version-counter';
                versionCounter.textContent = `${message.currentVersionIndex + 1} / ${message.versions.length}`;

                const aiReplyIndicator = document.createElement('span');
                aiReplyIndicator.className = 'version-ai-indicator';
                this.updateAIReplyIndicator(aiReplyIndicator, message.versions[message.currentVersionIndex]);

                versionInfo.appendChild(versionCounter);
                versionInfo.appendChild(aiReplyIndicator);

                const nextBtn = document.createElement('button');
                nextBtn.className = 'btn btn-icon version-btn version-next-btn';
                nextBtn.innerHTML = '▶';
                nextBtn.title = '下一个版本';

                // 设置按钮状态
                this.updateButtonStates(prevBtn, nextBtn, message);

                // 绑定事件
                prevBtn.addEventListener('click', () => this.switchVersion(message, -1));
                nextBtn.addEventListener('click', () => this.switchVersion(message, 1));

                versionControls.appendChild(prevBtn);
                versionControls.appendChild(versionInfo);
                versionControls.appendChild(nextBtn);

                return versionControls;
            }

            // 更新AI回复指示器
            updateAIReplyIndicator(indicator, version) {
                if (version.aiReply) {
                    const status = version.aiReply.status;
                    switch (status) {
                        case 'completed':
                            indicator.textContent = '🤖';
                            indicator.title = 'AI回复已完成';
                            break;
                        case 'generating':
                            indicator.textContent = '⏳';
                            indicator.title = 'AI回复生成中';
                            break;
                        case 'error':
                            indicator.textContent = '❌';
                            indicator.title = 'AI回复生成失败';
                            break;
                        default:
                            indicator.textContent = '🤖';
                            indicator.title = 'AI回复';
                    }
                } else {
                    indicator.textContent = '💬';
                    indicator.title = '仅用户消息';
                }
            }

            // 更新按钮状态
            updateButtonStates(prevBtn, nextBtn, message) {
                const isFirst = message.currentVersionIndex === 0;
                const isLast = message.currentVersionIndex === message.versions.length - 1;

                prevBtn.disabled = isFirst;
                if (isFirst) {
                    prevBtn.classList.add('disabled');
                } else {
                    prevBtn.classList.remove('disabled');
                }

                nextBtn.disabled = isLast;
                if (isLast) {
                    nextBtn.classList.add('disabled');
                } else {
                    nextBtn.classList.remove('disabled');
                }
            }

            // 切换版本
            switchVersion(message, direction) {
                const newIndex = message.currentVersionIndex + direction;
                if (newIndex < 0 || newIndex >= message.versions.length) {
                    return;
                }

                const oldIndex = message.currentVersionIndex;
                message.currentVersionIndex = newIndex;

                // 更新显示
                this.updateMessageDisplay(message);

                console.log('版本切换:', {
                    messageId: message.id,
                    from: oldIndex + 1,
                    to: newIndex + 1
                });
            }

            // 更新消息显示
            updateMessageDisplay(message) {
                const currentVersion = message.versions[message.currentVersionIndex];
                
                // 更新内容
                const contentElement = document.getElementById('demoMessageContent');
                if (contentElement) {
                    contentElement.textContent = currentVersion.content;
                }

                // 更新AI回复
                const aiReplyElement = document.getElementById('demoAIReply');
                if (aiReplyElement) {
                    if (currentVersion.aiReply) {
                        aiReplyElement.textContent = `AI回复: ${currentVersion.aiReply.content}`;
                        aiReplyElement.style.display = 'block';
                    } else {
                        aiReplyElement.style.display = 'none';
                    }
                }

                // 更新版本控件
                const controlsElement = document.getElementById('demoVersionControls');
                if (controlsElement) {
                    controlsElement.innerHTML = '';
                    const newControls = this.createVersionControls(message);
                    controlsElement.appendChild(newControls);
                }
            }

            // 获取版本历史
            getVersionHistory(message) {
                return message.versions.map((version, index) => ({
                    index,
                    id: version.id,
                    content: version.content,
                    timestamp: version.timestamp,
                    isCurrent: index === message.currentVersionIndex,
                    hasAIReply: !!version.aiReply,
                    aiReplyStatus: version.aiReply ? version.aiReply.status : null,
                    formattedTime: new Date(version.timestamp).toLocaleTimeString(),
                    contentPreview: version.content.length > 50 ? 
                        version.content.substring(0, 50) + '...' : 
                        version.content
                }));
            }

            // 跳转到版本
            jumpToVersion(message, targetIndex) {
                if (targetIndex < 0 || targetIndex >= message.versions.length) {
                    return false;
                }
                
                message.currentVersionIndex = targetIndex;
                this.updateMessageDisplay(message);
                return true;
            }

            // 复制版本
            duplicateVersion(message, versionIndex) {
                const sourceVersion = message.versions[versionIndex];
                const newVersion = {
                    id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    content: sourceVersion.content + ' (副本)',
                    timestamp: Date.now(),
                    aiReply: null,
                    metadata: { duplicatedFrom: sourceVersion.id }
                };

                const insertIndex = message.currentVersionIndex + 1;
                message.versions.splice(insertIndex, 0, newVersion);
                message.currentVersionIndex = insertIndex;
                this.updateMessageDisplay(message);
                return true;
            }

            // 删除版本
            deleteVersion(message, versionIndex) {
                if (message.versions.length <= 1) {
                    return false;
                }

                message.versions.splice(versionIndex, 1);
                if (message.currentVersionIndex >= versionIndex) {
                    message.currentVersionIndex = Math.max(0, message.currentVersionIndex - 1);
                }
                this.updateMessageDisplay(message);
                return true;
            }
        }

        // 全局管理器实例
        const versionManager = new VersionManager();

        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 导出测试函数到全局作用域
        window.createSingleVersionMessage = createSingleVersionMessage;
        window.createMultiVersionMessage = createMultiVersionMessage;
        window.testVersionSwitching = testVersionSwitching;
        window.testVersionControls = testVersionControls;
        window.testJumpToVersion = testJumpToVersion;
        window.testDuplicateVersion = testDuplicateVersion;
        window.testDeleteVersion = testDeleteVersion;
        window.testVersionHistory = testVersionHistory;
        window.testVersionWithAIReply = testVersionWithAIReply;
        window.testVersionSwitchWithAI = testVersionSwitchWithAI;
        window.testAIReplyStates = testAIReplyStates;
        window.showVersionHistory = showVersionHistory;
        window.updateVersionHistory = updateVersionHistory;
        window.clearVersionHistory = clearVersionHistory;

        // 1. 版本控件测试
        function createSingleVersionMessage() {
            versionManager.testMessage = versionManager.createTestMessage('这是一个单版本消息');

            const demoElement = document.getElementById('versionControlsDemo');
            demoElement.style.display = 'block';

            versionManager.updateMessageDisplay(versionManager.testMessage);
            showResult('controlsResult', '✅ 单版本消息已创建', 'success');
        }

        function createMultiVersionMessage() {
            const versions = [
                {
                    id: 'ver_1',
                    content: '第一个版本的内容',
                    timestamp: Date.now() - 3000,
                    aiReply: {
                        id: 'ai_1',
                        content: '第一个版本的AI回复',
                        status: 'completed',
                        timestamp: Date.now() - 2900
                    },
                    metadata: {}
                },
                {
                    id: 'ver_2',
                    content: '第二个版本的内容（编辑后）',
                    timestamp: Date.now() - 2000,
                    aiReply: {
                        id: 'ai_2',
                        content: '第二个版本的AI回复',
                        status: 'completed',
                        timestamp: Date.now() - 1900
                    },
                    metadata: {}
                },
                {
                    id: 'ver_3',
                    content: '第三个版本的内容（再次编辑）',
                    timestamp: Date.now() - 1000,
                    aiReply: null,
                    metadata: {}
                }
            ];

            versionManager.testMessage = versionManager.createTestMessage('', versions);
            versionManager.testMessage.currentVersionIndex = 0;

            const demoElement = document.getElementById('versionControlsDemo');
            demoElement.style.display = 'block';

            versionManager.updateMessageDisplay(versionManager.testMessage);
            showResult('controlsResult', '✅ 多版本消息已创建（3个版本）', 'success');
        }

        function testVersionSwitching() {
            if (!versionManager.testMessage || versionManager.testMessage.versions.length < 2) {
                showResult('controlsResult', '❌ 请先创建多版本消息', 'error');
                return;
            }

            let currentIndex = 0;
            const maxIndex = versionManager.testMessage.versions.length - 1;

            const switchInterval = setInterval(() => {
                currentIndex++;
                if (currentIndex > maxIndex) {
                    clearInterval(switchInterval);
                    showResult('controlsResult', '✅ 版本切换演示完成', 'success');
                    return;
                }

                versionManager.jumpToVersion(versionManager.testMessage, currentIndex);
            }, 1000);

            showResult('controlsResult', '🔄 正在演示版本切换...', 'info');
        }

        function testVersionControls() {
            if (!versionManager.testMessage) {
                showResult('controlsResult', '❌ 请先创建消息', 'error');
                return;
            }

            const controls = versionManager.createVersionControls(versionManager.testMessage);
            const result = `版本控件测试结果:
- 当前版本: ${versionManager.testMessage.currentVersionIndex + 1}/${versionManager.testMessage.versions.length}
- 控件元素: ${controls.children.length} 个子元素
- 按钮状态: 正常
- AI回复指示器: 正常`;

            showResult('controlsResult', result, 'success');
        }

        // 2. 版本操作测试
        function testJumpToVersion() {
            if (!versionManager.testMessage || versionManager.testMessage.versions.length < 2) {
                showResult('operationsResult', '❌ 请先创建多版本消息', 'error');
                return;
            }

            const targetIndex = Math.floor(Math.random() * versionManager.testMessage.versions.length);
            const success = versionManager.jumpToVersion(versionManager.testMessage, targetIndex);

            if (success) {
                showResult('operationsResult', `✅ 已跳转到版本 ${targetIndex + 1}`, 'success');
            } else {
                showResult('operationsResult', '❌ 跳转失败', 'error');
            }
        }

        function testDuplicateVersion() {
            if (!versionManager.testMessage) {
                showResult('operationsResult', '❌ 请先创建消息', 'error');
                return;
            }

            const currentIndex = versionManager.testMessage.currentVersionIndex;
            const success = versionManager.duplicateVersion(versionManager.testMessage, currentIndex);

            if (success) {
                showResult('operationsResult', `✅ 版本 ${currentIndex + 1} 已复制，总版本数: ${versionManager.testMessage.versions.length}`, 'success');
            } else {
                showResult('operationsResult', '❌ 复制失败', 'error');
            }
        }

        function testDeleteVersion() {
            if (!versionManager.testMessage || versionManager.testMessage.versions.length <= 1) {
                showResult('operationsResult', '❌ 需要多个版本才能删除', 'error');
                return;
            }

            const currentIndex = versionManager.testMessage.currentVersionIndex;
            const success = versionManager.deleteVersion(versionManager.testMessage, currentIndex);

            if (success) {
                showResult('operationsResult', `✅ 版本已删除，剩余版本数: ${versionManager.testMessage.versions.length}`, 'success');
            } else {
                showResult('operationsResult', '❌ 删除失败', 'error');
            }
        }

        function testVersionHistory() {
            if (!versionManager.testMessage) {
                showResult('operationsResult', '❌ 请先创建消息', 'error');
                return;
            }

            const history = versionManager.getVersionHistory(versionManager.testMessage);
            const result = `版本历史:
${history.map(v => `版本 ${v.index + 1}: ${v.contentPreview} ${v.isCurrent ? '(当前)' : ''} ${v.hasAIReply ? '🤖' : '💬'}`).join('\n')}`;

            showResult('operationsResult', result, 'info');
        }

        // 3. AI回复版本关联测试
        function testVersionWithAIReply() {
            const versions = [{
                id: 'ver_ai_1',
                content: '带AI回复的版本',
                timestamp: Date.now(),
                aiReply: {
                    id: 'ai_reply_1',
                    content: '这是对应的AI回复内容',
                    status: 'completed',
                    timestamp: Date.now(),
                    progress: 100,
                    metadata: { model: 'GPT-4', tokens: 150 }
                },
                metadata: {}
            }];

            versionManager.testMessage = versionManager.createTestMessage('', versions);

            const demoElement = document.getElementById('versionControlsDemo');
            demoElement.style.display = 'block';

            versionManager.updateMessageDisplay(versionManager.testMessage);
            showResult('aiReplyResult', '✅ 带AI回复的版本已创建', 'success');
        }

        function testVersionSwitchWithAI() {
            const versions = [
                {
                    id: 'ver_ai_1',
                    content: '版本1：有AI回复',
                    timestamp: Date.now() - 2000,
                    aiReply: {
                        id: 'ai_1',
                        content: '版本1的AI回复',
                        status: 'completed',
                        timestamp: Date.now() - 1900
                    },
                    metadata: {}
                },
                {
                    id: 'ver_ai_2',
                    content: '版本2：无AI回复',
                    timestamp: Date.now() - 1000,
                    aiReply: null,
                    metadata: {}
                },
                {
                    id: 'ver_ai_3',
                    content: '版本3：AI回复生成中',
                    timestamp: Date.now(),
                    aiReply: {
                        id: 'ai_3',
                        content: '正在生成...',
                        status: 'generating',
                        progress: 45,
                        timestamp: Date.now()
                    },
                    metadata: {}
                }
            ];

            versionManager.testMessage = versionManager.createTestMessage('', versions);

            const demoElement = document.getElementById('versionControlsDemo');
            demoElement.style.display = 'block';

            versionManager.updateMessageDisplay(versionManager.testMessage);

            // 自动切换演示
            let index = 0;
            const switchInterval = setInterval(() => {
                index = (index + 1) % versions.length;
                versionManager.jumpToVersion(versionManager.testMessage, index);

                if (index === 0) {
                    clearInterval(switchInterval);
                }
            }, 1500);

            showResult('aiReplyResult', '✅ AI回复版本切换演示开始', 'success');
        }

        function testAIReplyStates() {
            const states = ['pending', 'generating', 'completed', 'error', 'cancelled'];
            const versions = states.map((status, index) => ({
                id: `ver_state_${index}`,
                content: `版本${index + 1}：${status}状态`,
                timestamp: Date.now() - (states.length - index) * 1000,
                aiReply: {
                    id: `ai_state_${index}`,
                    content: status === 'completed' ? `${status}状态的AI回复` : '',
                    status: status,
                    progress: status === 'completed' ? 100 : (status === 'generating' ? 50 : 0),
                    timestamp: Date.now() - (states.length - index) * 1000
                },
                metadata: {}
            }));

            versionManager.testMessage = versionManager.createTestMessage('', versions);

            const demoElement = document.getElementById('versionControlsDemo');
            demoElement.style.display = 'block';

            versionManager.updateMessageDisplay(versionManager.testMessage);
            showResult('aiReplyResult', `✅ AI回复状态测试已创建（${states.length}种状态）`, 'success');
        }

        // 4. 版本历史可视化
        function showVersionHistory() {
            if (!versionManager.testMessage) {
                showResult('historyResult', '❌ 请先创建消息', 'error');
                return;
            }

            const history = versionManager.getVersionHistory(versionManager.testMessage);
            const historyElement = document.getElementById('versionHistory');
            const contentElement = document.getElementById('versionHistoryContent');

            contentElement.innerHTML = history.map(version => `
                <div class="version-item ${version.isCurrent ? 'current' : ''}">
                    <div class="version-content">
                        <div><strong>版本 ${version.index + 1}</strong></div>
                        <div>${version.contentPreview}</div>
                    </div>
                    <div class="version-meta">
                        <span>${version.formattedTime}</span>
                        <span>${version.hasAIReply ? '🤖' : '💬'}</span>
                        ${version.aiReplyStatus ? `<span>${version.aiReplyStatus}</span>` : ''}
                    </div>
                    <div class="version-actions">
                        <button onclick="jumpToVersionFromHistory(${version.index})">跳转</button>
                        <button onclick="duplicateVersionFromHistory(${version.index})">复制</button>
                        ${history.length > 1 ? `<button onclick="deleteVersionFromHistory(${version.index})">删除</button>` : ''}
                    </div>
                </div>
            `).join('');

            historyElement.style.display = 'block';
            showResult('historyResult', '✅ 版本历史已显示', 'success');
        }

        function updateVersionHistory() {
            if (document.getElementById('versionHistory').style.display === 'block') {
                showVersionHistory();
                showResult('historyResult', '✅ 版本历史已更新', 'success');
            } else {
                showResult('historyResult', '❌ 请先显示版本历史', 'error');
            }
        }

        function clearVersionHistory() {
            const historyElement = document.getElementById('versionHistory');
            historyElement.style.display = 'none';
            showResult('historyResult', '✅ 版本历史已清空', 'info');
        }

        // 版本历史操作函数
        window.jumpToVersionFromHistory = function(index) {
            versionManager.jumpToVersion(versionManager.testMessage, index);
            updateVersionHistory();
        };

        window.duplicateVersionFromHistory = function(index) {
            versionManager.duplicateVersion(versionManager.testMessage, index);
            updateVersionHistory();
        };

        window.deleteVersionFromHistory = function(index) {
            versionManager.deleteVersion(versionManager.testMessage, index);
            updateVersionHistory();
        };
    </script>
</body>
</html>
    </script>
</body>
</html>
