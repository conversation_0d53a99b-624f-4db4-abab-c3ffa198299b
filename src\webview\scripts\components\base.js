/**
 * 组件基类
 * 提供组件生命周期管理和通用功能
 */

import { DOMUtils } from '../utils/dom.js';
import { EventManager } from '../utils/events.js';

/**
 * 组件基类
 */
export class BaseComponent {
  constructor(element, options = {}) {
    this.element = element;
    this.options = { ...this.getDefaultOptions(), ...options };
    this.state = this.getInitialState();
    this.eventManager = new EventManager();
    this.isInitialized = false;
    this.isDestroyed = false;
    
    // 生成唯一ID
    this.id = this.generateId();
    
    // 绑定方法上下文
    this.init = this.init.bind(this);
    this.destroy = this.destroy.bind(this);
    this.setState = this.setState.bind(this);
    this.getState = this.getState.bind(this);
    this.render = this.render.bind(this);
  }

  /**
   * 获取默认选项
   * @returns {object} 默认选项
   */
  getDefaultOptions() {
    return {
      autoInit: true,
      className: '',
      attributes: {}
    };
  }

  /**
   * 获取初始状态
   * @returns {object} 初始状态
   */
  getInitialState() {
    return {
      visible: true,
      disabled: false,
      loading: false
    };
  }

  /**
   * 初始化组件
   * @returns {Promise} 初始化Promise
   */
  async init() {
    if (this.isInitialized) {
      console.warn(`Component ${this.constructor.name} already initialized`);
      return;
    }

    try {
      // 生命周期：beforeInit
      await this.beforeInit();
      
      // 设置元素属性
      this.setupElement();
      
      // 绑定事件
      this.bindEvents();
      
      // 初始渲染
      await this.render();
      
      // 生命周期：afterInit
      await this.afterInit();
      
      this.isInitialized = true;
      this.emit('init', this);
      
      console.log(`Component ${this.constructor.name} initialized`);
    } catch (error) {
      console.error(`Failed to initialize component ${this.constructor.name}:`, error);
      throw error;
    }
  }

  /**
   * 初始化前钩子
   */
  async beforeInit() {
    // 子类可以重写此方法
  }

  /**
   * 初始化后钩子
   */
  async afterInit() {
    // 子类可以重写此方法
  }

  /**
   * 设置元素属性
   */
  setupElement() {
    if (!this.element) return;

    // 设置组件ID
    if (!this.element.id) {
      this.element.id = this.id;
    }

    // 设置CSS类
    if (this.options.className) {
      DOMUtils.addClass(this.element, this.options.className);
    }

    // 设置属性
    Object.entries(this.options.attributes).forEach(([key, value]) => {
      this.element.setAttribute(key, value);
    });

    // 设置组件标识
    this.element.setAttribute('data-component', this.constructor.name);
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 子类可以重写此方法
  }

  /**
   * 渲染组件
   * @returns {Promise} 渲染Promise
   */
  async render() {
    if (!this.element) return;

    try {
      // 生命周期：beforeRender
      await this.beforeRender();
      
      // 执行渲染
      await this.doRender();
      
      // 生命周期：afterRender
      await this.afterRender();
      
      this.emit('render', this);
    } catch (error) {
      console.error(`Failed to render component ${this.constructor.name}:`, error);
      throw error;
    }
  }

  /**
   * 渲染前钩子
   */
  async beforeRender() {
    // 子类可以重写此方法
  }

  /**
   * 执行渲染
   */
  async doRender() {
    // 子类必须重写此方法
  }

  /**
   * 渲染后钩子
   */
  async afterRender() {
    // 子类可以重写此方法
  }

  /**
   * 设置状态
   * @param {string|object} key - 状态键或状态对象
   * @param {any} value - 状态值
   * @param {boolean} shouldRender - 是否触发重新渲染
   */
  setState(key, value, shouldRender = true) {
    const oldState = { ...this.state };

    if (typeof key === 'object') {
      this.state = { ...this.state, ...key };
    } else {
      this.state[key] = value;
    }

    this.emit('stateChange', { oldState, newState: this.state });

    if (shouldRender && this.isInitialized) {
      this.render();
    }
  }

  /**
   * 获取状态
   * @param {string} key - 状态键
   * @returns {any} 状态值
   */
  getState(key = null) {
    if (key === null) {
      return { ...this.state };
    }
    return this.state[key];
  }

  /**
   * 显示组件
   */
  show() {
    this.setState('visible', true);
    if (this.element) {
      DOMUtils.removeClass(this.element, 'hidden');
    }
    this.emit('show', this);
  }

  /**
   * 隐藏组件
   */
  hide() {
    this.setState('visible', false);
    if (this.element) {
      DOMUtils.addClass(this.element, 'hidden');
    }
    this.emit('hide', this);
  }

  /**
   * 切换显示/隐藏
   */
  toggle() {
    if (this.state.visible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 启用组件
   */
  enable() {
    this.setState('disabled', false);
    if (this.element) {
      this.element.removeAttribute('disabled');
      DOMUtils.removeClass(this.element, 'disabled');
    }
    this.emit('enable', this);
  }

  /**
   * 禁用组件
   */
  disable() {
    this.setState('disabled', true);
    if (this.element) {
      this.element.setAttribute('disabled', 'true');
      DOMUtils.addClass(this.element, 'disabled');
    }
    this.emit('disable', this);
  }

  /**
   * 设置加载状态
   * @param {boolean} loading - 是否加载中
   */
  setLoading(loading) {
    this.setState('loading', loading);
    if (this.element) {
      DOMUtils.toggleClass(this.element, 'loading', loading);
    }
    this.emit('loadingChange', loading);
  }

  /**
   * 触发事件
   * @param {string} event - 事件名
   * @param {...any} args - 参数
   */
  emit(event, ...args) {
    this.eventManager.emit(event, ...args);
  }

  /**
   * 监听事件
   * @param {string} event - 事件名
   * @param {function} listener - 监听器
   * @returns {function} 取消监听函数
   */
  on(event, listener) {
    return this.eventManager.on(event, listener);
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名
   * @param {function} listener - 监听器
   */
  off(event, listener) {
    this.eventManager.off(event, listener);
  }

  /**
   * 一次性事件监听
   * @param {string} event - 事件名
   * @param {function} listener - 监听器
   * @returns {Promise} Promise对象
   */
  once(event, listener) {
    return this.eventManager.once(event, listener);
  }

  /**
   * 查找子元素
   * @param {string} selector - 选择器
   * @returns {Element|null} 元素
   */
  find(selector) {
    return this.element ? DOMUtils.query(selector, this.element) : null;
  }

  /**
   * 查找所有子元素
   * @param {string} selector - 选择器
   * @returns {NodeList} 元素列表
   */
  findAll(selector) {
    return this.element ? DOMUtils.queryAll(selector, this.element) : [];
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    const prefix = this.constructor.name.toLowerCase();
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * 销毁组件
   */
  destroy() {
    if (this.isDestroyed) {
      console.warn(`Component ${this.constructor.name} already destroyed`);
      return;
    }

    try {
      // 生命周期：beforeDestroy
      this.beforeDestroy();
      
      // 移除事件监听
      this.eventManager.removeAllListeners();
      
      // 清理DOM
      if (this.element) {
        this.element.removeAttribute('data-component');
      }
      
      // 生命周期：afterDestroy
      this.afterDestroy();
      
      this.isDestroyed = true;
      this.isInitialized = false;
      
      console.log(`Component ${this.constructor.name} destroyed`);
    } catch (error) {
      console.error(`Failed to destroy component ${this.constructor.name}:`, error);
    }
  }

  /**
   * 销毁前钩子
   */
  beforeDestroy() {
    // 子类可以重写此方法
  }

  /**
   * 销毁后钩子
   */
  afterDestroy() {
    // 子类可以重写此方法
  }
}

/**
 * 组件工厂
 */
export class ComponentFactory {
  constructor() {
    this.components = new Map();
  }

  /**
   * 注册组件
   * @param {string} name - 组件名
   * @param {class} ComponentClass - 组件类
   */
  register(name, ComponentClass) {
    this.components.set(name, ComponentClass);
  }

  /**
   * 创建组件
   * @param {string} name - 组件名
   * @param {Element} element - 元素
   * @param {object} options - 选项
   * @returns {BaseComponent} 组件实例
   */
  create(name, element, options = {}) {
    const ComponentClass = this.components.get(name);
    if (!ComponentClass) {
      throw new Error(`Component "${name}" not registered`);
    }
    
    return new ComponentClass(element, options);
  }

  /**
   * 获取已注册的组件
   * @returns {Array} 组件名列表
   */
  getRegistered() {
    return Array.from(this.components.keys());
  }
}

// 创建全局组件工厂
export const globalComponentFactory = new ComponentFactory();

// 导出默认基类
export default BaseComponent;
