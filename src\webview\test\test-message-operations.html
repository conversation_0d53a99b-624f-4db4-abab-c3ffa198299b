<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息操作测试</title>
    <link rel="stylesheet" href="../styles/variables.css">
    <link rel="stylesheet" href="../styles/components.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.success {
            background: #28a745;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .demo-message {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            background: #fafafa;
        }
        .demo-message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .demo-message-content {
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .demo-ai-reply {
            margin-left: 20px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 4px;
            border-left: 3px solid #007acc;
            margin-top: 10px;
        }
        .search-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
        }
        .search-item {
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #007acc;
        }
        .search-item.ai-match {
            border-left-color: #28a745;
        }
        .search-item.metadata-match {
            border-left-color: #ffc107;
        }
        .search-meta {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .copy-demo {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .copy-content {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>消息操作测试</h1>
        <p>测试新的消息操作系统，包括复制、删除、编辑、搜索等功能</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 复制功能测试</h3>
            <div class="controls">
                <button class="button" onclick="testBasicCopy()">基础复制测试</button>
                <button class="button" onclick="testCopyWithAIReply()">复制含AI回复</button>
                <button class="button" onclick="testCopyWithMetadata()">复制含元数据</button>
                <button class="button" onclick="showCopyDemo()">显示复制内容</button>
            </div>
            <div id="copyDemo" class="copy-demo" style="display: none;">
                <h4>复制内容预览</h4>
                <div id="copyContent" class="copy-content"></div>
            </div>
            <div id="copyResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 删除功能测试</h3>
            <div class="controls">
                <button class="button danger" onclick="testDeleteUserMessage()">删除用户消息</button>
                <button class="button danger" onclick="testDeleteWithAIReply()">删除含AI回复消息</button>
                <button class="button danger" onclick="testDeleteAnalysis()">删除影响分析</button>
                <button class="button" onclick="testDeleteValidation()">删除验证</button>
            </div>
            <div id="deleteResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 编辑功能测试</h3>
            <div class="controls">
                <button class="button" onclick="testBasicEdit()">基础编辑测试</button>
                <button class="button" onclick="testEditWithVersions()">编辑创建版本</button>
                <button class="button" onclick="testEditValidation()">编辑验证</button>
                <button class="button" onclick="testEditWithAIRegeneration()">编辑+AI重新生成</button>
            </div>
            <div id="editResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 搜索功能测试</h3>
            <div class="controls">
                <button class="button" onclick="testBasicSearch()">基础搜索</button>
                <button class="button" onclick="testSearchAIReplies()">搜索AI回复</button>
                <button class="button" onclick="testAdvancedSearch()">高级搜索</button>
                <button class="button" onclick="testSearchAllVersions()">搜索所有版本</button>
            </div>
            <div id="searchResults" class="search-results" style="display: none;">
                <h4>搜索结果</h4>
                <div id="searchResultsContent"></div>
            </div>
            <div id="searchResult" class="result"></div>
        </div>
    </div>

    <script type="module">
        // 模拟消息操作管理器
        class MessageOperationsManager {
            constructor() {
                this.messages = [];
                this.clipboard = '';
            }

            // 创建测试消息
            createTestMessage(content, hasAIReply = false, aiReplyData = {}) {
                const message = {
                    id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    isUser: true,
                    timestamp: Date.now(),
                    edited: false,
                    versions: [{
                        id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                        content: content,
                        timestamp: Date.now(),
                        aiReply: hasAIReply ? {
                            id: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                            content: aiReplyData.content || 'AI回复内容',
                            timestamp: Date.now(),
                            status: aiReplyData.status || 'completed',
                            progress: aiReplyData.progress || 100,
                            metadata: {
                                model: aiReplyData.model || 'GPT-4',
                                tokens: aiReplyData.tokens || 150,
                                duration: aiReplyData.duration || 2000,
                                ...aiReplyData.metadata
                            }
                        } : null,
                        metadata: {}
                    }],
                    currentVersionIndex: 0,
                    metadata: {}
                };
                this.messages.push(message);
                return message;
            }

            // 复制消息
            copyMessage(message) {
                const currentVersion = message.versions[message.currentVersionIndex];
                let contentToCopy = `用户: ${currentVersion.content}`;
                
                if (currentVersion.aiReply && currentVersion.aiReply.content) {
                    const aiReply = currentVersion.aiReply;
                    contentToCopy += `\n\nAI回复: ${aiReply.content}`;
                    
                    if (aiReply.metadata && (aiReply.metadata.model || aiReply.metadata.tokens)) {
                        const metadata = [];
                        if (aiReply.metadata.model) metadata.push(`模型: ${aiReply.metadata.model}`);
                        if (aiReply.metadata.tokens) metadata.push(`Token: ${aiReply.metadata.tokens}`);
                        if (aiReply.metadata.duration) {
                            const duration = (aiReply.metadata.duration / 1000).toFixed(1);
                            metadata.push(`耗时: ${duration}s`);
                        }
                        
                        if (metadata.length > 0) {
                            contentToCopy += `\n(${metadata.join(', ')})`;
                        }
                    }
                }

                this.clipboard = contentToCopy;
                return contentToCopy;
            }

            // 删除消息分析
            analyzeMessageDeletion(message) {
                const analysis = {
                    versionsCount: message.versions ? message.versions.length : 0,
                    aiReplyCount: 0,
                    hasAIReplies: false,
                    activeAIReplies: 0,
                    completedAIReplies: 0
                };

                if (message.versions) {
                    message.versions.forEach(version => {
                        if (version.aiReply) {
                            analysis.aiReplyCount++;
                            analysis.hasAIReplies = true;
                            
                            if (version.aiReply.status === 'completed') {
                                analysis.completedAIReplies++;
                            } else if (['pending', 'generating'].includes(version.aiReply.status)) {
                                analysis.activeAIReplies++;
                            }
                        }
                    });
                }

                return analysis;
            }

            // 删除消息
            deleteMessage(messageId) {
                const messageIndex = this.messages.findIndex(m => m.id === messageId);
                if (messageIndex === -1) return null;

                const message = this.messages[messageIndex];
                if (!message.isUser) {
                    throw new Error('不允许删除独立的AI消息');
                }

                const deleteInfo = this.analyzeMessageDeletion(message);
                this.messages.splice(messageIndex, 1);
                
                return { message, deleteInfo };
            }

            // 编辑消息
            editMessage(messageId, newContent) {
                const message = this.messages.find(m => m.id === messageId);
                if (!message) return null;

                if (!message.isUser) {
                    throw new Error('不允许编辑AI消息');
                }

                const currentVersion = message.versions[message.currentVersionIndex];
                if (newContent === currentVersion.content) {
                    return null; // 内容没有变化
                }

                const newVersion = {
                    id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
                    content: newContent,
                    timestamp: Date.now(),
                    aiReply: null,
                    metadata: {
                        editReason: 'user_edit',
                        editType: 'manual',
                        previousVersionId: currentVersion.id,
                        editedAt: Date.now()
                    }
                };

                message.versions.push(newVersion);
                message.currentVersionIndex = message.versions.length - 1;
                message.edited = true;
                message.editedAt = Date.now();

                return { message, newVersion, previousVersionIndex: message.versions.length - 2 };
            }

            // 搜索消息
            searchMessages(query, options = {}) {
                const {
                    caseSensitive = false,
                    includeAIReplies = true,
                    includeMetadata = false,
                    searchAllVersions = false
                } = options;

                const searchQuery = caseSensitive ? query : query.toLowerCase();
                const results = [];

                this.messages.forEach(message => {
                    if (!message.isUser) return;

                    const versionsToSearch = searchAllVersions ? 
                        message.versions : 
                        [message.versions[message.currentVersionIndex]];

                    versionsToSearch.forEach((version, versionIndex) => {
                        if (!version) return;

                        // 搜索用户消息内容
                        const userContent = caseSensitive ? version.content : version.content.toLowerCase();
                        if (userContent.includes(searchQuery)) {
                            results.push({
                                type: 'user',
                                messageId: message.id,
                                versionId: version.id,
                                versionIndex: searchAllVersions ? versionIndex : message.currentVersionIndex,
                                content: version.content,
                                timestamp: version.timestamp,
                                matchType: 'content',
                                preview: this.createSearchPreview(version.content, query)
                            });
                        }

                        // 搜索AI回复内容
                        if (includeAIReplies && version.aiReply && version.aiReply.content) {
                            const aiContent = caseSensitive ? 
                                version.aiReply.content : 
                                version.aiReply.content.toLowerCase();
                            
                            if (aiContent.includes(searchQuery)) {
                                results.push({
                                    type: 'ai',
                                    messageId: message.id,
                                    versionId: version.id,
                                    versionIndex: searchAllVersions ? versionIndex : message.currentVersionIndex,
                                    aiReplyId: version.aiReply.id,
                                    content: version.aiReply.content,
                                    timestamp: version.aiReply.timestamp,
                                    matchType: 'ai_reply',
                                    preview: this.createSearchPreview(version.aiReply.content, query),
                                    aiStatus: version.aiReply.status
                                });
                            }
                        }

                        // 搜索元数据
                        if (includeMetadata) {
                            const metadata = JSON.stringify(version.metadata || {});
                            const metadataContent = caseSensitive ? metadata : metadata.toLowerCase();
                            
                            if (metadataContent.includes(searchQuery)) {
                                results.push({
                                    type: 'metadata',
                                    messageId: message.id,
                                    versionId: version.id,
                                    versionIndex: searchAllVersions ? versionIndex : message.currentVersionIndex,
                                    content: metadata,
                                    timestamp: version.timestamp,
                                    matchType: 'metadata',
                                    preview: this.createSearchPreview(metadata, query)
                                });
                            }
                        }
                    });
                });

                return results.sort((a, b) => b.timestamp - a.timestamp);
            }

            // 创建搜索预览
            createSearchPreview(content, query) {
                const maxLength = 100;
                const queryIndex = content.toLowerCase().indexOf(query.toLowerCase());
                
                if (queryIndex === -1) {
                    return content.length > maxLength ? 
                        content.substring(0, maxLength) + '...' : 
                        content;
                }

                const start = Math.max(0, queryIndex - 30);
                const end = Math.min(content.length, queryIndex + query.length + 30);
                
                let preview = content.substring(start, end);
                if (start > 0) preview = '...' + preview;
                if (end < content.length) preview = preview + '...';
                
                return preview;
            }
        }

        // 全局管理器实例
        const operationsManager = new MessageOperationsManager();

        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 导出测试函数到全局作用域
        window.testBasicCopy = testBasicCopy;
        window.testCopyWithAIReply = testCopyWithAIReply;
        window.testCopyWithMetadata = testCopyWithMetadata;
        window.showCopyDemo = showCopyDemo;
        window.testDeleteUserMessage = testDeleteUserMessage;
        window.testDeleteWithAIReply = testDeleteWithAIReply;
        window.testDeleteAnalysis = testDeleteAnalysis;
        window.testDeleteValidation = testDeleteValidation;
        window.testBasicEdit = testBasicEdit;
        window.testEditWithVersions = testEditWithVersions;
        window.testEditValidation = testEditValidation;
        window.testEditWithAIRegeneration = testEditWithAIRegeneration;
        window.testBasicSearch = testBasicSearch;
        window.testSearchAIReplies = testSearchAIReplies;
        window.testAdvancedSearch = testAdvancedSearch;
        window.testSearchAllVersions = testSearchAllVersions;

        // 1. 复制功能测试
        function testBasicCopy() {
            const message = operationsManager.createTestMessage('这是一个基础测试消息');
            const copiedContent = operationsManager.copyMessage(message);

            showResult('copyResult', `✅ 基础复制测试完成\n复制内容长度: ${copiedContent.length} 字符`, 'success');
        }

        function testCopyWithAIReply() {
            const message = operationsManager.createTestMessage('用户问题：什么是JavaScript？', true, {
                content: 'JavaScript是一种高级的、解释型的编程语言，主要用于网页开发。',
                model: 'GPT-4',
                tokens: 150,
                duration: 2500
            });

            const copiedContent = operationsManager.copyMessage(message);

            showResult('copyResult', `✅ 含AI回复复制测试完成\n复制内容包含用户消息和AI回复\n总长度: ${copiedContent.length} 字符`, 'success');
        }

        function testCopyWithMetadata() {
            const message = operationsManager.createTestMessage('测试消息', true, {
                content: '详细的AI回复内容',
                model: 'GPT-4-Turbo',
                tokens: 200,
                duration: 3200
            });

            const copiedContent = operationsManager.copyMessage(message);
            const hasMetadata = copiedContent.includes('模型:') && copiedContent.includes('Token:');

            showResult('copyResult', `✅ 含元数据复制测试完成\n包含元数据: ${hasMetadata ? '是' : '否'}\n总长度: ${copiedContent.length} 字符`, 'success');
        }

        function showCopyDemo() {
            const message = operationsManager.createTestMessage('演示消息：请解释人工智能', true, {
                content: '人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。',
                model: 'GPT-4',
                tokens: 180,
                duration: 2800
            });

            const copiedContent = operationsManager.copyMessage(message);

            const demoElement = document.getElementById('copyDemo');
            const contentElement = document.getElementById('copyContent');

            contentElement.textContent = copiedContent;
            demoElement.style.display = 'block';

            showResult('copyResult', '✅ 复制内容演示已显示', 'info');
        }

        // 2. 删除功能测试
        function testDeleteUserMessage() {
            const message = operationsManager.createTestMessage('要删除的用户消息');

            try {
                const result = operationsManager.deleteMessage(message.id);
                showResult('deleteResult', `✅ 用户消息删除成功\n消息ID: ${result.message.id}\n版本数: ${result.deleteInfo.versionsCount}`, 'success');
            } catch (error) {
                showResult('deleteResult', `❌ 删除失败: ${error.message}`, 'error');
            }
        }

        function testDeleteWithAIReply() {
            const message = operationsManager.createTestMessage('含AI回复的消息', true, {
                content: '这是AI回复内容'
            });

            try {
                const result = operationsManager.deleteMessage(message.id);
                showResult('deleteResult', `✅ 含AI回复消息删除成功\n版本数: ${result.deleteInfo.versionsCount}\nAI回复数: ${result.deleteInfo.aiReplyCount}\n已完成AI回复: ${result.deleteInfo.completedAIReplies}`, 'success');
            } catch (error) {
                showResult('deleteResult', `❌ 删除失败: ${error.message}`, 'error');
            }
        }

        function testDeleteAnalysis() {
            // 创建多版本消息
            const message = operationsManager.createTestMessage('原始消息', true);

            // 添加更多版本
            message.versions.push({
                id: 'ver_2',
                content: '编辑后的消息',
                timestamp: Date.now(),
                aiReply: {
                    id: 'ai_2',
                    content: '第二个AI回复',
                    status: 'completed',
                    timestamp: Date.now()
                },
                metadata: {}
            });

            message.versions.push({
                id: 'ver_3',
                content: '再次编辑的消息',
                timestamp: Date.now(),
                aiReply: {
                    id: 'ai_3',
                    content: '正在生成...',
                    status: 'generating',
                    timestamp: Date.now()
                },
                metadata: {}
            });

            const analysis = operationsManager.analyzeMessageDeletion(message);

            showResult('deleteResult', `✅ 删除分析完成\n版本数: ${analysis.versionsCount}\nAI回复总数: ${analysis.aiReplyCount}\n已完成: ${analysis.completedAIReplies}\n活跃中: ${analysis.activeAIReplies}`, 'info');
        }

        function testDeleteValidation() {
            // 测试删除不存在的消息
            try {
                operationsManager.deleteMessage('non-existent-id');
                showResult('deleteResult', '❌ 应该抛出错误但没有', 'error');
            } catch (error) {
                showResult('deleteResult', '✅ 删除验证正常：正确处理不存在的消息', 'success');
            }
        }

        // 3. 编辑功能测试
        function testBasicEdit() {
            const message = operationsManager.createTestMessage('原始消息内容');

            try {
                const result = operationsManager.editMessage(message.id, '编辑后的消息内容');

                if (result) {
                    showResult('editResult', `✅ 基础编辑测试完成\n新版本ID: ${result.newVersion.id}\n总版本数: ${result.message.versions.length}\n当前版本: ${result.message.currentVersionIndex + 1}`, 'success');
                } else {
                    showResult('editResult', '❌ 编辑失败：没有返回结果', 'error');
                }
            } catch (error) {
                showResult('editResult', `❌ 编辑失败: ${error.message}`, 'error');
            }
        }

        function testEditWithVersions() {
            const message = operationsManager.createTestMessage('版本1内容');

            // 进行多次编辑
            operationsManager.editMessage(message.id, '版本2内容');
            const result = operationsManager.editMessage(message.id, '版本3内容');

            showResult('editResult', `✅ 多版本编辑测试完成\n总版本数: ${result.message.versions.length}\n当前版本: ${result.message.currentVersionIndex + 1}\n最新内容: ${result.newVersion.content}`, 'success');
        }

        function testEditValidation() {
            const message = operationsManager.createTestMessage('测试消息');

            // 测试相同内容编辑
            const result = operationsManager.editMessage(message.id, '测试消息');

            if (result === null) {
                showResult('editResult', '✅ 编辑验证正常：相同内容不创建新版本', 'success');
            } else {
                showResult('editResult', '❌ 编辑验证失败：应该返回null', 'error');
            }
        }

        function testEditWithAIRegeneration() {
            const message = operationsManager.createTestMessage('原始问题', true, {
                content: '原始AI回复'
            });

            const result = operationsManager.editMessage(message.id, '修改后的问题');

            // 检查新版本是否没有AI回复（需要重新生成）
            const hasAIReply = result.newVersion.aiReply !== null;

            showResult('editResult', `✅ 编辑+AI重新生成测试完成\n新版本AI回复: ${hasAIReply ? '存在' : '需要重新生成'}\n编辑原因: ${result.newVersion.metadata.editReason}`, 'success');
        }

        // 4. 搜索功能测试
        function testBasicSearch() {
            // 创建测试数据
            operationsManager.createTestMessage('JavaScript是一种编程语言');
            operationsManager.createTestMessage('Python也是编程语言');
            operationsManager.createTestMessage('学习编程很有趣');

            const results = operationsManager.searchMessages('编程');

            showResult('searchResult', `✅ 基础搜索测试完成\n搜索关键词: "编程"\n找到结果: ${results.length} 条`, 'success');

            displaySearchResults(results);
        }

        function testSearchAIReplies() {
            // 创建含AI回复的消息
            operationsManager.createTestMessage('什么是机器学习？', true, {
                content: '机器学习是人工智能的一个分支，它使计算机能够学习和改进。'
            });

            operationsManager.createTestMessage('解释深度学习', true, {
                content: '深度学习是机器学习的一个子集，使用神经网络。'
            });

            const results = operationsManager.searchMessages('机器学习', {
                includeAIReplies: true
            });

            const aiMatches = results.filter(r => r.type === 'ai').length;
            const userMatches = results.filter(r => r.type === 'user').length;

            showResult('searchResult', `✅ AI回复搜索测试完成\n搜索关键词: "机器学习"\n用户消息匹配: ${userMatches} 条\nAI回复匹配: ${aiMatches} 条`, 'success');

            displaySearchResults(results);
        }

        function testAdvancedSearch() {
            // 创建复杂测试数据
            const message = operationsManager.createTestMessage('高级搜索测试', true, {
                content: '这是一个高级搜索功能的演示'
            });

            // 添加元数据
            message.versions[0].metadata = {
                category: '测试',
                priority: 'high',
                tags: ['搜索', '高级', '演示']
            };

            const results = operationsManager.searchMessages('高级', {
                includeAIReplies: true,
                includeMetadata: true,
                caseSensitive: false
            });

            const metadataMatches = results.filter(r => r.type === 'metadata').length;

            showResult('searchResult', `✅ 高级搜索测试完成\n搜索关键词: "高级"\n总匹配: ${results.length} 条\n元数据匹配: ${metadataMatches} 条`, 'success');

            displaySearchResults(results);
        }

        function testSearchAllVersions() {
            // 创建多版本消息
            const message = operationsManager.createTestMessage('版本搜索测试');

            // 添加更多版本
            message.versions.push({
                id: 'ver_2',
                content: '这是第二个版本，包含特殊关键词',
                timestamp: Date.now(),
                aiReply: null,
                metadata: {}
            });

            message.versions.push({
                id: 'ver_3',
                content: '第三个版本的内容',
                timestamp: Date.now(),
                aiReply: {
                    id: 'ai_3',
                    content: '第三版本的AI回复，也包含特殊关键词',
                    status: 'completed',
                    timestamp: Date.now()
                },
                metadata: {}
            });

            const currentResults = operationsManager.searchMessages('特殊关键词', {
                searchAllVersions: false
            });

            const allVersionsResults = operationsManager.searchMessages('特殊关键词', {
                searchAllVersions: true,
                includeAIReplies: true
            });

            showResult('searchResult', `✅ 版本搜索测试完成\n搜索关键词: "特殊关键词"\n当前版本搜索: ${currentResults.length} 条\n所有版本搜索: ${allVersionsResults.length} 条`, 'success');

            displaySearchResults(allVersionsResults);
        }

        // 显示搜索结果
        function displaySearchResults(results) {
            const resultsElement = document.getElementById('searchResults');
            const contentElement = document.getElementById('searchResultsContent');

            if (results.length === 0) {
                contentElement.innerHTML = '<div class="search-item">没有找到匹配的结果</div>';
            } else {
                contentElement.innerHTML = results.map(result => `
                    <div class="search-item ${result.type}-match">
                        <div><strong>${result.type === 'user' ? '用户消息' : result.type === 'ai' ? 'AI回复' : '元数据'}</strong></div>
                        <div>${result.preview}</div>
                        <div class="search-meta">
                            版本: ${result.versionIndex + 1} |
                            时间: ${new Date(result.timestamp).toLocaleTimeString()}
                            ${result.aiStatus ? ` | 状态: ${result.aiStatus}` : ''}
                        </div>
                    </div>
                `).join('');
            }

            resultsElement.style.display = 'block';
        }

        // 初始化一些测试数据
        operationsManager.createTestMessage('欢迎使用消息操作测试系统');
        operationsManager.createTestMessage('这里可以测试各种消息操作功能', true, {
            content: '包括复制、删除、编辑和搜索等功能。'
        });
    </script>
</body>
</html>
    </script>
</body>
</html>
