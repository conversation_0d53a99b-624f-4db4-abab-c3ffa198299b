<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局和样式统一测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .layout-demo {
            border: 2px dashed var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            position: relative;
        }
        
        .layout-demo::before {
            content: attr(data-label);
            position: absolute;
            top: -10px;
            left: var(--spacing-md);
            background: var(--color-surface);
            padding: 0 var(--spacing-sm);
            font-size: var(--font-size-xs);
            color: var(--color-foreground-muted);
        }
        
        .width-indicator {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--color-primary);
            color: var(--color-primary-foreground);
            padding: var(--spacing-xs);
            font-size: var(--font-size-xs);
            border-radius: 0 var(--radius-md) 0 var(--radius-sm);
        }
        
        .responsive-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .viewport-simulator {
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            overflow: hidden;
            position: relative;
        }
        
        .viewport-header {
            background: var(--color-surface-hover);
            padding: var(--spacing-sm);
            font-size: var(--font-size-xs);
            color: var(--color-foreground-muted);
            text-align: center;
        }
        
        .viewport-content {
            height: 300px;
            overflow: hidden;
            transform-origin: top left;
        }
        
        .chat-layout-demo {
            height: 400px;
            display: flex;
            flex-direction: column;
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            overflow: hidden;
        }
        
        .demo-messages-container {
            flex: 1;
            overflow-y: auto;
            padding: var(--spacing-md);
            background-color: var(--color-background);
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .demo-messages-wrapper {
            width: 100%;
            max-width: var(--message-container-max-width);
        }
        
        .demo-input-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--spacing-md);
            border-top: 1px solid var(--color-border);
            background-color: var(--color-surface);
        }
        
        .demo-input-wrapper {
            display: flex;
            align-items: flex-end;
            gap: var(--spacing-sm);
            width: 100%;
            max-width: var(--message-container-max-width);
        }
        
        .demo-message {
            display: flex;
            margin-bottom: var(--spacing-md);
            gap: var(--spacing-md);
        }
        
        .demo-message-user {
            align-items: flex-end;
            max-width: var(--message-user-max-width);
            margin-left: auto;
        }
        
        .demo-message-ai {
            align-items: flex-start;
            max-width: var(--message-ai-max-width);
        }
        
        .demo-message-content {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            position: relative;
        }
        
        .demo-message-user .demo-message-content {
            background: var(--color-primary);
            color: var(--color-primary-foreground);
        }
        
        .width-test-controls {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
            flex-wrap: wrap;
        }
        
        .width-test-btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--color-border);
            background: var(--color-surface);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: var(--font-size-xs);
            transition: all var(--transition-fast);
        }
        
        .width-test-btn:hover {
            background: var(--color-surface-hover);
        }
        
        .width-test-btn.active {
            background: var(--color-primary);
            color: var(--color-primary-foreground);
            border-color: var(--color-primary);
        }
        
        .measurement-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            border: 2px solid var(--color-primary);
            border-radius: var(--radius-md);
            opacity: 0;
            transition: opacity var(--transition-fast);
        }
        
        .measurement-overlay.active {
            opacity: 0.5;
        }
        
        .measurement-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--color-primary);
            color: var(--color-primary-foreground);
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>布局和样式统一测试</h1>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="testAllLayouts()">测试所有布局</button>
                <button class="btn btn-secondary" onclick="testResponsive()">测试响应式</button>
                <button class="btn btn-secondary" onclick="measureWidths()">测量宽度</button>
                <button class="btn btn-secondary" onclick="testScrolling()">测试滚动</button>
                <button class="btn btn-secondary" onclick="addTestMessages()">添加测试消息</button>
                <button class="btn btn-ghost" onclick="clearTestMessages()">清空消息</button>
            </div>
        </div>
        
        <!-- 宽度测试 -->
        <div class="test-section">
            <h2>宽度统一测试</h2>
            <div class="width-test-controls">
                <div class="width-test-btn active" onclick="setTestWidth('auto')">自动宽度</div>
                <div class="width-test-btn" onclick="setTestWidth('1200px')">1200px</div>
                <div class="width-test-btn" onclick="setTestWidth('900px')">900px</div>
                <div class="width-test-btn" onclick="setTestWidth('600px')">600px</div>
                <div class="width-test-btn" onclick="setTestWidth('400px')">400px</div>
            </div>
            
            <div class="layout-demo" data-label="消息容器宽度测试" id="widthTestDemo">
                <div class="width-indicator" id="widthIndicator">Auto</div>
                <div class="measurement-overlay" id="measurementOverlay">
                    <div class="measurement-label" id="measurementLabel">测量中...</div>
                </div>
                
                <div class="chat-layout-demo" id="chatLayoutDemo">
                    <div class="demo-messages-container">
                        <div class="demo-messages-wrapper" id="demoMessagesWrapper">
                            <div class="demo-message demo-message-user">
                                <div class="demo-message-content">
                                    这是一条用户消息，用来测试宽度限制。
                                </div>
                            </div>
                            <div class="demo-message demo-message-ai">
                                <div class="demo-message-content">
                                    这是一条AI回复消息，用来测试不同的宽度设置。AI消息通常比用户消息稍宽一些。
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="demo-input-container">
                        <div class="demo-input-wrapper">
                            <input type="text" class="input" placeholder="输入消息..." style="flex: 1;">
                            <button class="btn btn-primary">发送</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 响应式测试 -->
        <div class="test-section">
            <h2>响应式布局测试</h2>
            <div class="responsive-test">
                <div class="viewport-simulator">
                    <div class="viewport-header">桌面 (1200px+)</div>
                    <div class="viewport-content" style="width: 1200px; transform: scale(0.25);">
                        <div class="chat-layout-demo" style="width: 1200px; height: 800px;">
                            <div class="demo-messages-container">
                                <div class="demo-messages-wrapper">
                                    <div class="demo-message demo-message-user">
                                        <div class="demo-message-content">桌面端用户消息</div>
                                    </div>
                                    <div class="demo-message demo-message-ai">
                                        <div class="demo-message-content">桌面端AI回复消息</div>
                                    </div>
                                </div>
                            </div>
                            <div class="demo-input-container">
                                <div class="demo-input-wrapper">
                                    <input type="text" class="input" placeholder="桌面端输入...">
                                    <button class="btn btn-primary">发送</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="viewport-simulator">
                    <div class="viewport-header">平板 (768px)</div>
                    <div class="viewport-content" style="width: 768px; transform: scale(0.39);">
                        <div class="chat-layout-demo" style="width: 768px; height: 800px;">
                            <div class="demo-messages-container">
                                <div class="demo-messages-wrapper">
                                    <div class="demo-message demo-message-user">
                                        <div class="demo-message-content">平板端用户消息</div>
                                    </div>
                                    <div class="demo-message demo-message-ai">
                                        <div class="demo-message-content">平板端AI回复消息</div>
                                    </div>
                                </div>
                            </div>
                            <div class="demo-input-container">
                                <div class="demo-input-wrapper">
                                    <input type="text" class="input" placeholder="平板端输入...">
                                    <button class="btn btn-primary">发送</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="viewport-simulator">
                    <div class="viewport-header">手机 (375px)</div>
                    <div class="viewport-content" style="width: 375px; transform: scale(0.8);">
                        <div class="chat-layout-demo" style="width: 375px; height: 600px;">
                            <div class="demo-messages-container">
                                <div class="demo-messages-wrapper">
                                    <div class="demo-message demo-message-user">
                                        <div class="demo-message-content">手机端用户消息</div>
                                    </div>
                                    <div class="demo-message demo-message-ai">
                                        <div class="demo-message-content">手机端AI回复消息</div>
                                    </div>
                                </div>
                            </div>
                            <div class="demo-input-container">
                                <div class="demo-input-wrapper">
                                    <input type="text" class="input" placeholder="手机端输入...">
                                    <button class="btn btn-primary">发送</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 实际聊天界面测试 -->
        <div class="test-section">
            <h2>实际聊天界面测试</h2>
            <div class="layout-demo" data-label="实际消息组件">
                <div class="chat-layout-demo">
                    <div class="messages-container" id="testMessages">
                        <div class="messages-wrapper">
                            <!-- 实际消息将在这里显示 -->
                        </div>
                    </div>
                    
                    <div class="input-container">
                        <div class="input-wrapper">
                            <textarea class="input message-input" 
                                      placeholder="输入消息..." 
                                      rows="1" 
                                      id="testMessageInput"></textarea>
                            <button class="btn btn-primary" onclick="sendTestMessage()">发送</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { Store } from './scripts/state/store.js';
        import { MessageComponent } from './scripts/components/message.js';
        import { ToastComponent } from './scripts/components/toast.js';
        import { EventManager } from './scripts/utils/events.js';
        import { MessageTypes, MessageStatus } from './scripts/components/message-manager.js';
        
        // 初始化组件
        const store = new Store();
        const eventManager = new EventManager();
        const toastComponent = new ToastComponent();
        
        // 创建消息组件实例
        const testMessagesContainer = document.getElementById('testMessages');
        const messageComponent = new MessageComponent(store, eventManager);
        
        // 全局变量
        window.store = store;
        window.messageComponent = messageComponent;
        window.toastComponent = toastComponent;
        window.currentTestWidth = 'auto';
        
        // 初始化
        Promise.all([
            messageComponent.init(testMessagesContainer),
            toastComponent.init()
        ]).then(() => {
            console.log('布局和样式统一测试页面初始化完成');
            updateWidthIndicator();
        }).catch(error => {
            console.error('初始化失败:', error);
        });
        
        // 测试函数
        window.setTestWidth = function(width) {
            currentTestWidth = width;
            
            const demo = document.getElementById('chatLayoutDemo');
            if (width === 'auto') {
                demo.style.width = '';
                demo.style.maxWidth = '';
            } else {
                demo.style.width = width;
                demo.style.maxWidth = width;
            }
            
            // 更新按钮状态
            document.querySelectorAll('.width-test-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            updateWidthIndicator();
        };
        
        function updateWidthIndicator() {
            const indicator = document.getElementById('widthIndicator');
            const demo = document.getElementById('chatLayoutDemo');
            const actualWidth = demo.offsetWidth;
            
            indicator.textContent = currentTestWidth === 'auto' 
                ? `Auto (${actualWidth}px)` 
                : `${currentTestWidth} (${actualWidth}px)`;
        }
        
        window.measureWidths = function() {
            const overlay = document.getElementById('measurementOverlay');
            const label = document.getElementById('measurementLabel');
            const wrapper = document.getElementById('demoMessagesWrapper');
            
            overlay.classList.add('active');
            
            const measurements = {
                container: document.getElementById('chatLayoutDemo').offsetWidth,
                wrapper: wrapper.offsetWidth,
                userMessage: wrapper.querySelector('.demo-message-user').offsetWidth,
                aiMessage: wrapper.querySelector('.demo-message-ai').offsetWidth
            };
            
            label.textContent = `容器: ${measurements.container}px | 包装器: ${measurements.wrapper}px | 用户: ${measurements.userMessage}px | AI: ${measurements.aiMessage}px`;
            
            setTimeout(() => {
                overlay.classList.remove('active');
            }, 3000);
        };
        
        window.addTestMessages = function() {
            const testMessages = [
                { content: '这是一条短消息', isUser: true },
                { content: '这是一条AI回复消息，内容稍微长一些，用来测试不同长度消息的布局效果。', isUser: false },
                { content: '这是一条很长的用户消息，包含了大量的文本内容，用来测试消息的最大宽度限制和换行效果。这条消息应该会在达到最大宽度时自动换行。', isUser: true },
                { content: '这是一条超长的AI回复消息，包含了更多的文本内容。这条消息用来测试AI消息的宽度设置是否合理，以及在不同屏幕尺寸下的显示效果。AI消息通常比用户消息稍宽一些，以便显示更多的信息内容。', isUser: false }
            ];
            
            testMessages.forEach((messageData, index) => {
                setTimeout(() => {
                    messageComponent.addMessage({
                        ...messageData,
                        type: MessageTypes.TEXT,
                        status: MessageStatus.SENT
                    });
                }, index * 500);
            });
        };
        
        window.sendTestMessage = function() {
            const input = document.getElementById('testMessageInput');
            const content = input.value.trim();
            
            if (!content) return;
            
            messageComponent.addMessage({
                content,
                isUser: true,
                type: MessageTypes.TEXT,
                status: MessageStatus.SENT
            });
            
            input.value = '';
            
            // 模拟AI回复
            setTimeout(() => {
                messageComponent.addMessage({
                    content: `这是对"${content}"的AI回复。`,
                    isUser: false,
                    type: MessageTypes.TEXT,
                    status: MessageStatus.SENT
                });
            }, 1000);
        };
        
        window.clearTestMessages = function() {
            messageComponent.clearMessages();
        };
        
        window.testAllLayouts = function() {
            const widths = ['auto', '1200px', '900px', '600px', '400px'];
            let index = 0;
            
            function testNext() {
                if (index < widths.length) {
                    setTestWidth(widths[index]);
                    setTimeout(() => {
                        measureWidths();
                        index++;
                        setTimeout(testNext, 2000);
                    }, 500);
                }
            }
            
            testNext();
        };
        
        window.testResponsive = function() {
            // 模拟不同屏幕尺寸
            const sizes = [
                { width: '1200px', label: '桌面' },
                { width: '900px', label: '小桌面' },
                { width: '768px', label: '平板' },
                { width: '480px', label: '手机' },
                { width: '375px', label: '小手机' }
            ];
            
            let index = 0;
            
            function testNextSize() {
                if (index < sizes.length) {
                    const size = sizes[index];
                    setTestWidth(size.width);
                    
                    setTimeout(() => {
                        measureWidths();
                        index++;
                        setTimeout(testNextSize, 2000);
                    }, 500);
                }
            }
            
            testNextSize();
        };
        
        window.testScrolling = function() {
            // 添加大量消息测试滚动
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    messageComponent.addMessage({
                        content: `测试滚动消息 ${i + 1}`,
                        isUser: i % 2 === 0,
                        type: MessageTypes.TEXT,
                        status: MessageStatus.SENT
                    });
                }, i * 100);
            }
        };
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            setTimeout(updateWidthIndicator, 100);
        });
        
        console.log('布局和样式统一测试页面已加载');
    </script>
</body>
</html>
