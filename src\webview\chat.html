<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src {{CSP_SOURCE}} 'unsafe-inline'; script-src {{CSP_SOURCE}} 'unsafe-inline';img-src {{CSP_SOURCE}} https: data:;">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>AI 聊天</title>

	<!-- 导入样式文件 -->
	<link rel="stylesheet" href="/src/webview/styles/variables.css">
	<link rel="stylesheet" href="/src/webview/styles/components.css">
	<link rel="stylesheet" href="/src/webview/styles/layout.css">
	<link rel="stylesheet" href="/src/webview/styles/themes.css">


</head>
<body>
	<div class="app-container" id="app">
		<!-- 聊天视图 -->
		<div class="chat-container" id="chatView">
			<div class="messages-container" id="messages">
				<div class="messages-wrapper">
					<!-- 消息将在这里显示 -->
				</div>
			</div>
			<div class="input-container">
				<div class="input-wrapper">
					<textarea id="messageTextarea" class="input input-field" placeholder="输入您的消息... (Enter/Shift+Enter换行，Ctrl+Enter发送)" rows="5"></textarea>
					<button id="sendBtn" class="send-button" title="发送消息">➤</button>
				</div>
			</div>
		</div>

		<!-- 设置视图 -->
		<div class="settings-container hidden" id="settingsView">
			<!-- 设置内容将由JavaScript动态生成 -->
		</div>

		<!-- Toast通知容器 -->
		<div class="toast-container" id="toastContainer">
			<!-- Toast通知将在这里显示 -->
		</div>
	</div>

	<!-- 导入模块化JavaScript -->
	<script type="module">
		// 导入主应用
		import ChatApp from '/src/webview/scripts/main.js';

		// 应用已在main.js中自动初始化
		console.log('Chat application loaded');
	</script>

	<script>
		// 兼容性脚本 - 为不支持ES6模块的环境提供降级支持
		if (!window.chatApp) {
			console.warn('ES6 modules not supported, loading fallback script');
			// 这里可以加载编译后的兼容性版本
		}

	</script>
</body>
</html>
