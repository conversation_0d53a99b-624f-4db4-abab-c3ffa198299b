/**
 * VS Code主题检测和适配工具
 */

/**
 * 主题类型枚举
 */
export const ThemeTypes = {
  LIGHT: 'light',
  DARK: 'dark',
  HIGH_CONTRAST: 'high-contrast',
  AUTO: 'auto'
};

/**
 * 常见VS Code主题枚举
 */
export const VSCodeThemes = {
  DEFAULT_LIGHT: 'Default Light+',
  DEFAULT_DARK: 'Default Dark+',
  ONE_DARK_PRO: 'One Dark Pro',
  DRACULA: 'Dracula',
  MATERIAL_THEME: 'Material Theme',
  MONOKAI: 'Monokai',
  SOLARIZED_LIGHT: 'Solarized Light',
  SOLARIZED_DARK: 'Solarized Dark',
  GITHUB_LIGHT: 'GitHub Light',
  GITHUB_DARK: 'GitHub Dark'
};

/**
 * 主题检测器类
 */
export class ThemeDetector {
  constructor() {
    this.currentTheme = null;
    this.currentThemeKind = null;
    this.currentThemeName = null;
    this.observers = [];
    this.colorSchemeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    this.contrastQuery = window.matchMedia('(prefers-contrast: high)');
    this.reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    this.init();
  }

  /**
   * 初始化主题检测
   */
  init() {
    // 检测当前主题
    this.detectTheme();
    
    // 监听系统主题变化
    this.colorSchemeQuery.addEventListener('change', () => {
      this.detectTheme();
    });
    
    this.contrastQuery.addEventListener('change', () => {
      this.detectTheme();
    });
    
    // 监听VS Code主题变化
    this.observeVSCodeTheme();
    
    // 设置初始主题
    this.applyTheme();
  }

  /**
   * 检测当前主题
   */
  detectTheme() {
    // 检查VS Code主题信息
    const body = document.body;
    const themeKind = body.getAttribute('data-vscode-theme-kind');
    const themeName = body.getAttribute('data-vscode-theme-name');
    
    if (themeKind) {
      this.currentThemeKind = themeKind;
      this.currentThemeName = themeName || 'Unknown';
      
      // 根据VS Code主题类型设置主题
      if (themeKind.includes('light')) {
        this.currentTheme = ThemeTypes.LIGHT;
      } else if (themeKind.includes('dark')) {
        this.currentTheme = ThemeTypes.DARK;
      } else if (themeKind.includes('high-contrast')) {
        this.currentTheme = ThemeTypes.HIGH_CONTRAST;
      } else {
        this.currentTheme = ThemeTypes.AUTO;
      }
    } else {
      // 回退到系统主题检测
      if (this.contrastQuery.matches) {
        this.currentTheme = ThemeTypes.HIGH_CONTRAST;
      } else if (this.colorSchemeQuery.matches) {
        this.currentTheme = ThemeTypes.DARK;
      } else {
        this.currentTheme = ThemeTypes.LIGHT;
      }
    }
    
    // 通知观察者
    this.notifyObservers();
  }

  /**
   * 观察VS Code主题变化
   */
  observeVSCodeTheme() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'data-vscode-theme-kind' || 
             mutation.attributeName === 'data-vscode-theme-name')) {
          this.detectTheme();
        }
      });
    });
    
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['data-vscode-theme-kind', 'data-vscode-theme-name']
    });
  }

  /**
   * 应用主题
   */
  applyTheme() {
    const html = document.documentElement;
    
    // 设置主题属性
    html.setAttribute('data-theme', this.currentTheme);
    
    // 设置主题类
    html.className = html.className.replace(/theme-\w+/g, '');
    html.classList.add(`theme-${this.currentTheme}`);
    
    // 设置CSS自定义属性
    this.setCSSProperties();
  }

  /**
   * 设置CSS自定义属性
   */
  setCSSProperties() {
    const root = document.documentElement;
    
    // 根据主题设置颜色RGB值
    const colorMappings = this.getColorMappings();
    
    Object.entries(colorMappings).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }

  /**
   * 获取颜色映射
   */
  getColorMappings() {
    const mappings = {};
    
    // 根据主题名称设置特定颜色
    if (this.currentThemeName) {
      if (this.currentThemeName.includes('One Dark')) {
        mappings['--color-primary-rgb'] = '97, 175, 239';
        mappings['--color-success-rgb'] = '152, 195, 121';
        mappings['--color-warning-rgb'] = '229, 192, 123';
        mappings['--color-error-rgb'] = '224, 108, 117';
      } else if (this.currentThemeName.includes('Dracula')) {
        mappings['--color-primary-rgb'] = '189, 147, 249';
        mappings['--color-success-rgb'] = '80, 250, 123';
        mappings['--color-warning-rgb'] = '241, 250, 140';
        mappings['--color-error-rgb'] = '255, 85, 85';
      } else if (this.currentThemeName.includes('Material')) {
        mappings['--color-primary-rgb'] = '130, 170, 255';
        mappings['--color-success-rgb'] = '195, 232, 141';
        mappings['--color-warning-rgb'] = '255, 203, 107';
        mappings['--color-error-rgb'] = '240, 113, 120';
      } else if (this.currentThemeName.includes('Monokai')) {
        mappings['--color-primary-rgb'] = '102, 217, 239';
        mappings['--color-success-rgb'] = '166, 226, 46';
        mappings['--color-warning-rgb'] = '230, 219, 116';
        mappings['--color-error-rgb'] = '249, 38, 114';
      } else if (this.currentThemeName.includes('Solarized')) {
        mappings['--color-primary-rgb'] = '38, 139, 210';
        mappings['--color-success-rgb'] = '133, 153, 0';
        mappings['--color-warning-rgb'] = '181, 137, 0';
        mappings['--color-error-rgb'] = '220, 50, 47';
      }
    }
    
    return mappings;
  }

  /**
   * 获取当前主题信息
   */
  getCurrentTheme() {
    return {
      type: this.currentTheme,
      kind: this.currentThemeKind,
      name: this.currentThemeName,
      isDark: this.currentTheme === ThemeTypes.DARK,
      isLight: this.currentTheme === ThemeTypes.LIGHT,
      isHighContrast: this.currentTheme === ThemeTypes.HIGH_CONTRAST,
      isAuto: this.currentTheme === ThemeTypes.AUTO
    };
  }

  /**
   * 检查是否为特定主题
   */
  isTheme(themeName) {
    return this.currentThemeName && 
           this.currentThemeName.toLowerCase().includes(themeName.toLowerCase());
  }

  /**
   * 检查颜色对比度
   */
  checkColorContrast(foreground, background) {
    // 简单的对比度检查（实际应用中可以使用更复杂的算法）
    const fgLuminance = this.getLuminance(foreground);
    const bgLuminance = this.getLuminance(background);
    
    const contrast = (Math.max(fgLuminance, bgLuminance) + 0.05) / 
                    (Math.min(fgLuminance, bgLuminance) + 0.05);
    
    return {
      ratio: contrast,
      isAccessible: contrast >= 4.5, // WCAG AA标准
      isEnhanced: contrast >= 7.1     // WCAG AAA标准
    };
  }

  /**
   * 计算颜色亮度
   */
  getLuminance(color) {
    // 简化的亮度计算
    const rgb = color.match(/\d+/g);
    if (!rgb) return 0;
    
    const [r, g, b] = rgb.map(c => {
      c = parseInt(c) / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /**
   * 添加主题变化观察者
   */
  addObserver(callback) {
    this.observers.push(callback);
  }

  /**
   * 移除主题变化观察者
   */
  removeObserver(callback) {
    const index = this.observers.indexOf(callback);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  /**
   * 通知观察者
   */
  notifyObservers() {
    const themeInfo = this.getCurrentTheme();
    this.observers.forEach(callback => {
      try {
        callback(themeInfo);
      } catch (error) {
        console.error('Theme observer error:', error);
      }
    });
    
    // 应用主题
    this.applyTheme();
  }

  /**
   * 获取主题统计信息
   */
  getThemeStats() {
    return {
      currentTheme: this.currentTheme,
      currentThemeKind: this.currentThemeKind,
      currentThemeName: this.currentThemeName,
      systemPrefersDark: this.colorSchemeQuery.matches,
      systemPrefersHighContrast: this.contrastQuery.matches,
      systemPrefersReducedMotion: this.reducedMotionQuery.matches,
      observerCount: this.observers.length
    };
  }
}

/**
 * 主题适配工具
 */
export class ThemeAdapter {
  /**
   * 为元素应用主题适配
   */
  static adaptElement(element, themeInfo) {
    if (!element || !themeInfo) return;
    
    // 根据主题类型添加类名
    element.classList.remove('theme-light', 'theme-dark', 'theme-high-contrast', 'theme-auto');
    element.classList.add(`theme-${themeInfo.type}`);
    
    // 高对比度模式特殊处理
    if (themeInfo.isHighContrast) {
      element.style.outline = '1px solid currentColor';
      element.style.outlineOffset = '1px';
    } else {
      element.style.outline = '';
      element.style.outlineOffset = '';
    }
  }

  /**
   * 获取主题适配的颜色
   */
  static getAdaptedColor(colorName, themeInfo) {
    const colorMappings = {
      [ThemeTypes.LIGHT]: {
        primary: '#0078d4',
        success: '#107c10',
        warning: '#ff8c00',
        error: '#d13438'
      },
      [ThemeTypes.DARK]: {
        primary: '#0078d4',
        success: '#89d185',
        warning: '#ffcc02',
        error: '#f85149'
      },
      [ThemeTypes.HIGH_CONTRAST]: {
        primary: '#ffffff',
        success: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000'
      }
    };
    
    return colorMappings[themeInfo.type]?.[colorName] || '#0078d4';
  }
}

// 创建全局主题检测器实例
export const globalThemeDetector = new ThemeDetector();

export default {
  ThemeTypes,
  VSCodeThemes,
  ThemeDetector,
  ThemeAdapter,
  globalThemeDetector
};
