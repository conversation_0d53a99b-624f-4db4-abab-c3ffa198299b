/**
 * 主题选择器组件
 * 提供可视化的主题选择和实时预览功能
 */

import BaseComponent from './base.js';
import { DOMUtils } from '../utils/dom.js';
import { globalThemeManager } from '../utils/theme.js';

/**
 * 主题选择器组件类
 */
export class ThemeSelectorComponent extends BaseComponent {
  constructor(container, options = {}) {
    super(container, {
      className: 'theme-selector-component',
      autoInit: true,
      ...options
    });
    
    this.themes = [
      {
        id: 'auto',
        name: '跟随系统',
        description: '根据系统设置自动切换主题',
        icon: '🔄',
        preview: 'linear-gradient(45deg, #f8f9fa 50%, #212529 50%)'
      },
      {
        id: 'light',
        name: '浅色主题',
        description: '明亮清爽的浅色界面',
        icon: '☀️',
        preview: '#f8f9fa'
      },
      {
        id: 'dark',
        name: '深色主题',
        description: '护眼舒适的深色界面',
        icon: '🌙',
        preview: '#212529'
      },
      {
        id: 'high-contrast',
        name: '高对比度',
        description: '高对比度主题，提升可读性',
        icon: '⚡',
        preview: 'linear-gradient(45deg, #000 50%, #fff 50%)'
      }
    ];
    
    this.currentTheme = 'auto';
    
    // 绑定方法上下文
    this.selectTheme = this.selectTheme.bind(this);
    this.updateSelection = this.updateSelection.bind(this);
  }

  /**
   * 获取初始状态
   */
  getInitialState() {
    return {
      ...super.getInitialState(),
      selectedTheme: 'auto',
      previewMode: false
    };
  }

  /**
   * 初始化后钩子
   */
  async afterInit() {
    // 获取当前主题
    this.currentTheme = globalThemeManager.getTheme();
    this.setState('selectedTheme', this.currentTheme);
    
    // 监听主题变化
    document.addEventListener('themechange', (e) => {
      this.currentTheme = e.detail.theme;
      this.setState('selectedTheme', this.currentTheme);
      this.updateSelection();
    });
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (!this.element) return;
    
    // 使用事件委托处理主题选择
    this.element.addEventListener('click', (e) => {
      const themeCard = e.target.closest('.theme-card');
      if (themeCard) {
        const themeId = themeCard.dataset.themeId;
        this.selectTheme(themeId);
      }
    });
    
    // 处理键盘导航
    this.element.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        const themeCard = e.target.closest('.theme-card');
        if (themeCard) {
          e.preventDefault();
          const themeId = themeCard.dataset.themeId;
          this.selectTheme(themeId);
        }
      }
    });
  }

  /**
   * 执行渲染
   */
  async doRender() {
    if (!this.element) return;
    
    this.element.innerHTML = `
      <div class="theme-selector-header">
        <h3 class="theme-selector-title">选择主题</h3>
        <p class="theme-selector-description">选择您喜欢的界面主题</p>
      </div>
      
      <div class="theme-grid">
        ${this.themes.map(theme => this.createThemeCard(theme)).join('')}
      </div>
      
      <div class="theme-selector-footer">
        <div class="current-theme-info">
          <span class="current-theme-label">当前主题：</span>
          <span class="current-theme-name">${this.getThemeName(this.currentTheme)}</span>
        </div>
      </div>
    `;
    
    // 更新选择状态
    this.updateSelection();
  }

  /**
   * 创建主题卡片
   * @param {object} theme - 主题对象
   * @returns {string} HTML字符串
   */
  createThemeCard(theme) {
    const isSelected = theme.id === this.currentTheme;
    
    return `
      <div class="theme-card ${isSelected ? 'selected' : ''}" 
           data-theme-id="${theme.id}"
           tabindex="0"
           role="button"
           aria-label="选择${theme.name}">
        <div class="theme-preview" style="background: ${theme.preview};">
          <div class="theme-icon">${theme.icon}</div>
        </div>
        <div class="theme-info">
          <div class="theme-name">${theme.name}</div>
          <div class="theme-description">${theme.description}</div>
        </div>
        <div class="theme-check">
          <div class="check-icon">${isSelected ? '✓' : ''}</div>
        </div>
      </div>
    `;
  }

  /**
   * 选择主题
   * @param {string} themeId - 主题ID
   */
  selectTheme(themeId) {
    if (themeId === this.currentTheme) return;
    
    // 应用主题
    globalThemeManager.setTheme(themeId);
    
    // 更新状态
    this.currentTheme = themeId;
    this.setState('selectedTheme', themeId);
    
    // 更新UI
    this.updateSelection();
    
    // 触发事件
    this.emit('themeSelected', {
      themeId,
      themeName: this.getThemeName(themeId)
    });
  }

  /**
   * 更新选择状态
   */
  updateSelection() {
    if (!this.element) return;
    
    // 移除所有选中状态
    const cards = this.element.querySelectorAll('.theme-card');
    cards.forEach(card => {
      card.classList.remove('selected');
      const checkIcon = card.querySelector('.check-icon');
      if (checkIcon) {
        checkIcon.textContent = '';
      }
    });
    
    // 添加当前选中状态
    const selectedCard = this.element.querySelector(`[data-theme-id="${this.currentTheme}"]`);
    if (selectedCard) {
      selectedCard.classList.add('selected');
      const checkIcon = selectedCard.querySelector('.check-icon');
      if (checkIcon) {
        checkIcon.textContent = '✓';
      }
    }
    
    // 更新当前主题信息
    const currentThemeName = this.element.querySelector('.current-theme-name');
    if (currentThemeName) {
      currentThemeName.textContent = this.getThemeName(this.currentTheme);
    }
  }

  /**
   * 获取主题名称
   * @param {string} themeId - 主题ID
   * @returns {string} 主题名称
   */
  getThemeName(themeId) {
    const theme = this.themes.find(t => t.id === themeId);
    return theme ? theme.name : '未知主题';
  }

  /**
   * 获取当前选中的主题
   * @returns {string} 主题ID
   */
  getSelectedTheme() {
    return this.currentTheme;
  }

  /**
   * 设置主题（外部调用）
   * @param {string} themeId - 主题ID
   */
  setTheme(themeId) {
    if (this.themes.find(t => t.id === themeId)) {
      this.selectTheme(themeId);
    }
  }
}

export default ThemeSelectorComponent;
