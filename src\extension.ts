// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import { ChatViewProvider } from './providers/ChatViewProvider';
import { registerCommands } from './commands';


// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	console.log('Congratulations, your extension "ai-extension-demo" is now active!');

	// Register the webview view provider
	const chatViewProvider = new ChatViewProvider(context.extensionUri);
	const disposableViewProvider = vscode.window.registerWebviewViewProvider(
		ChatViewProvider.viewType,
		chatViewProvider,
		{
			webviewOptions: {
				retainContextWhenHidden: true
			}
		}
	);
	context.subscriptions.push(disposableViewProvider);

	console.log('WebviewViewProvider registered for:', ChatViewProvider.viewType);

	// Register all commands using the commands module
	registerCommands(context, chatViewProvider);
}

// This method is called when your extension is deactivated
export function deactivate() {}
