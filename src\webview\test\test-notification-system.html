<!DOCTYPE html>
<html lang="zh-CN" data-theme="auto" data-font-size="md">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知系统测试</title>
    
    <!-- 导入样式文件 -->
    <link rel="stylesheet" href="./styles/variables.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/layout.css">
    <link rel="stylesheet" href="./styles/themes.css">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .test-section h2 {
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            color: var(--color-foreground);
        }
        
        .control-panel {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
        }
        
        .demo-panel {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-lg);
        }
        
        .notification-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .type-button {
            padding: var(--spacing-md);
            border: 2px solid var(--color-border);
            border-radius: var(--radius-md);
            background: var(--color-surface);
            cursor: pointer;
            text-align: center;
            transition: all var(--transition-fast);
        }
        
        .type-button:hover {
            border-color: var(--color-primary);
            background: var(--color-surface-hover);
        }
        
        .type-button.success {
            border-color: var(--color-success);
            color: var(--color-success);
        }
        
        .type-button.warning {
            border-color: var(--color-warning);
            color: var(--color-warning);
        }
        
        .type-button.error {
            border-color: var(--color-error);
            color: var(--color-error);
        }
        
        .type-button.info {
            border-color: var(--color-primary);
            color: var(--color-primary);
        }
        
        .type-button.loading {
            border-color: var(--color-primary);
            color: var(--color-primary);
        }
        
        .toolbar-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }
        
        .stats-display {
            background: var(--color-surface-hover);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            font-family: monospace;
            font-size: var(--font-size-sm);
        }
        
        .history-list {
            max-height: 200px;
            overflow-y: auto;
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-sm);
        }
        
        .history-item {
            padding: var(--spacing-xs);
            border-bottom: 1px solid var(--color-border);
            font-size: var(--font-size-sm);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-time {
            color: var(--color-foreground-muted);
            font-size: var(--font-size-xs);
        }
        
        .test-input {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .test-input input {
            flex: 1;
        }
        
        .position-selector {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-md);
        }
        
        .position-btn {
            padding: var(--spacing-xs);
            border: 1px solid var(--color-border);
            background: var(--color-surface);
            cursor: pointer;
            text-align: center;
            font-size: var(--font-size-xs);
            transition: all var(--transition-fast);
        }
        
        .position-btn:hover {
            background: var(--color-surface-hover);
        }
        
        .position-btn.active {
            background: var(--color-primary);
            color: var(--color-primary-foreground);
            border-color: var(--color-primary);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>通知系统测试</h1>
        
        <!-- 控制面板 -->
        <div class="test-section">
            <h2>基础控制</h2>
            <div class="control-panel">
                <button class="btn btn-primary" onclick="testAllTypes()">测试所有类型</button>
                <button class="btn btn-secondary" onclick="testQueue()">测试队列</button>
                <button class="btn btn-secondary" onclick="testGrouping()">测试分组</button>
                <button class="btn btn-secondary" onclick="testProgress()">测试进度</button>
                <button class="btn btn-secondary" onclick="testBatch()">测试批量</button>
                <button class="btn btn-ghost" onclick="clearAllNotifications()">清空通知</button>
            </div>
        </div>
        
        <!-- 通知类型测试 -->
        <div class="test-section">
            <h2>通知类型测试</h2>
            <div class="notification-types">
                <div class="type-button success" onclick="showNotification('success')">
                    <div>✓</div>
                    <div>成功</div>
                </div>
                <div class="type-button warning" onclick="showNotification('warning')">
                    <div>⚠</div>
                    <div>警告</div>
                </div>
                <div class="type-button error" onclick="showNotification('error')">
                    <div>✕</div>
                    <div>错误</div>
                </div>
                <div class="type-button info" onclick="showNotification('info')">
                    <div>ℹ</div>
                    <div>信息</div>
                </div>
                <div class="type-button loading" onclick="showNotification('loading')">
                    <div>⟳</div>
                    <div>加载</div>
                </div>
            </div>
        </div>
        
        <!-- 工具栏操作测试 -->
        <div class="test-section">
            <h2>工具栏操作测试</h2>
            <div class="toolbar-actions">
                <button class="btn btn-sm" onclick="testToolbarAction('copy')">复制</button>
                <button class="btn btn-sm" onclick="testToolbarAction('edit')">编辑</button>
                <button class="btn btn-sm" onclick="testToolbarAction('delete')">删除</button>
                <button class="btn btn-sm" onclick="testToolbarAction('save')">保存</button>
                <button class="btn btn-sm" onclick="testToolbarAction('export')">导出</button>
                <button class="btn btn-sm" onclick="testToolbarAction('import')">导入</button>
                <button class="btn btn-sm" onclick="testToolbarAction('reset')">重置</button>
                <button class="btn btn-sm" onclick="testToolbarAction('search')">搜索</button>
                <button class="btn btn-sm" onclick="testConfirmAction()">确认操作</button>
            </div>
        </div>
        
        <!-- 高级功能测试 -->
        <div class="test-section">
            <h2>高级功能测试</h2>
            <div class="demo-grid">
                <!-- 自定义通知 -->
                <div class="demo-panel">
                    <h3>自定义通知</h3>
                    <div class="test-input">
                        <input type="text" id="customMessage" class="input" placeholder="自定义消息" value="这是一条自定义通知">
                        <select id="customType" class="input">
                            <option value="success">成功</option>
                            <option value="warning">警告</option>
                            <option value="error">错误</option>
                            <option value="info" selected>信息</option>
                            <option value="loading">加载</option>
                        </select>
                        <button class="btn btn-primary" onclick="showCustomNotification()">显示</button>
                    </div>
                    
                    <div class="test-input">
                        <input type="number" id="customDuration" class="input" placeholder="持续时间(ms)" value="3000" min="0">
                        <label>
                            <input type="checkbox" id="customClosable" checked> 可关闭
                        </label>
                        <label>
                            <input type="checkbox" id="customPersistent"> 持久化
                        </label>
                    </div>
                </div>
                
                <!-- 位置设置 -->
                <div class="demo-panel">
                    <h3>通知位置</h3>
                    <div class="position-selector">
                        <button class="position-btn" onclick="setPosition('top-left')">左上</button>
                        <button class="position-btn" onclick="setPosition('top-center')">上中</button>
                        <button class="position-btn active" onclick="setPosition('top-right')">右上</button>
                        <button class="position-btn" onclick="setPosition('bottom-left')">左下</button>
                        <button class="position-btn" onclick="setPosition('bottom-center')">下中</button>
                        <button class="position-btn" onclick="setPosition('bottom-right')">右下</button>
                    </div>
                    
                    <div style="margin-top: var(--spacing-md);">
                        <label>
                            最大通知数: 
                            <input type="number" id="maxNotifications" value="5" min="1" max="10" style="width: 60px;">
                        </label>
                        <button class="btn btn-sm" onclick="updateConfig()">应用</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 统计和历史 -->
        <div class="test-section">
            <h2>统计和历史</h2>
            <div class="demo-grid">
                <!-- 统计信息 -->
                <div class="demo-panel">
                    <h3>统计信息</h3>
                    <div id="statsDisplay" class="stats-display">
                        <!-- 统计信息将在这里显示 -->
                    </div>
                    <button class="btn btn-secondary" onclick="refreshStats()">刷新统计</button>
                </div>
                
                <!-- 通知历史 -->
                <div class="demo-panel">
                    <h3>通知历史</h3>
                    <div id="historyList" class="history-list">
                        <!-- 历史记录将在这里显示 -->
                    </div>
                    <div style="margin-top: var(--spacing-sm);">
                        <button class="btn btn-secondary" onclick="refreshHistory()">刷新历史</button>
                        <button class="btn btn-ghost" onclick="clearHistory()">清空历史</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Toast容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <script type="module">
        import { globalNotificationManager, NotificationTypes, NotificationPositions } from './scripts/components/notification-manager.js';
        import { globalToolbarNotifications, ToolbarActions } from './scripts/components/toolbar-notifications.js';
        
        // 全局变量
        window.notificationManager = globalNotificationManager;
        window.toolbarNotifications = globalToolbarNotifications;
        window.testCounter = 0;
        
        // 初始化
        globalNotificationManager.init().then(() => {
            console.log('通知系统测试页面初始化完成');
            refreshStats();
            refreshHistory();
        }).catch(error => {
            console.error('初始化失败:', error);
        });
        
        // 显示通知
        window.showNotification = function(type) {
            const messages = {
                success: '操作成功完成！',
                warning: '请注意这个警告信息。',
                error: '发生了一个错误，请重试。',
                info: '这是一条信息通知。',
                loading: '正在处理，请稍候...'
            };
            
            const options = type === 'loading' ? { persistent: true, closable: true } : {};
            
            globalNotificationManager.show(messages[type], type, options);
        };
        
        // 显示自定义通知
        window.showCustomNotification = function() {
            const message = document.getElementById('customMessage').value || '自定义通知';
            const type = document.getElementById('customType').value;
            const duration = parseInt(document.getElementById('customDuration').value) || 3000;
            const closable = document.getElementById('customClosable').checked;
            const persistent = document.getElementById('customPersistent').checked;
            
            const options = {
                duration: persistent ? 0 : duration,
                closable,
                persistent
            };
            
            globalNotificationManager.show(message, type, options);
        };
        
        // 测试工具栏操作
        window.testToolbarAction = function(action) {
            const actions = ['success', 'error', 'loading'];
            const randomAction = actions[Math.floor(Math.random() * actions.length)];
            
            if (randomAction === 'loading') {
                const loadingId = globalToolbarNotifications.notifyLoading(action);
                
                // 模拟异步操作
                setTimeout(() => {
                    globalNotificationManager.hide(loadingId);
                    globalToolbarNotifications.notifySuccess(action);
                }, 2000);
            } else {
                globalToolbarNotifications[`notify${randomAction.charAt(0).toUpperCase() + randomAction.slice(1)}`](action);
            }
        };
        
        // 测试确认操作
        window.testConfirmAction = function() {
            globalToolbarNotifications.notifyConfirm(
                ToolbarActions.DELETE,
                () => {
                    globalToolbarNotifications.notifySuccess(ToolbarActions.DELETE, '删除操作已确认');
                },
                () => {
                    globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '删除操作已取消');
                }
            );
        };
        
        // 测试所有类型
        window.testAllTypes = function() {
            const types = ['success', 'warning', 'error', 'info', 'loading'];
            
            types.forEach((type, index) => {
                setTimeout(() => {
                    showNotification(type);
                }, index * 500);
            });
        };
        
        // 测试队列
        window.testQueue = function() {
            // 快速显示多个通知，测试队列功能
            for (let i = 0; i < 8; i++) {
                setTimeout(() => {
                    globalNotificationManager.show(`队列测试通知 ${i + 1}`, 'info', {
                        duration: 2000
                    });
                }, i * 100);
            }
        };
        
        // 测试分组
        window.testGrouping = function() {
            // 显示相同分组的通知，应该会更新而不是创建新的
            globalNotificationManager.show('第一条分组消息', 'info', { groupKey: 'test-group' });
            
            setTimeout(() => {
                globalNotificationManager.show('第二条分组消息（应该更新第一条）', 'success', { groupKey: 'test-group' });
            }, 1000);
            
            setTimeout(() => {
                globalNotificationManager.show('第三条分组消息（应该再次更新）', 'warning', { groupKey: 'test-group' });
            }, 2000);
        };
        
        // 测试进度
        window.testProgress = function() {
            let progress = 0;
            const progressId = globalToolbarNotifications.notifyProgress('export', progress, '正在导出数据');
            
            const interval = setInterval(() => {
                progress += 10;
                
                if (progress <= 100) {
                    globalToolbarNotifications.notifyProgress('export', progress, '正在导出数据');
                } else {
                    clearInterval(interval);
                    globalNotificationManager.hide(progressId);
                    globalToolbarNotifications.notifySuccess('export', '数据导出完成');
                }
            }, 300);
        };
        
        // 测试批量操作
        window.testBatch = function() {
            let completed = 0;
            let failed = 0;
            const total = 10;
            
            const interval = setInterval(() => {
                if (completed + failed < total) {
                    if (Math.random() > 0.2) {
                        completed++;
                    } else {
                        failed++;
                    }
                    
                    globalToolbarNotifications.notifyBatch('处理', total, completed, failed);
                } else {
                    clearInterval(interval);
                }
            }, 500);
        };
        
        // 设置位置
        window.setPosition = function(position) {
            globalNotificationManager.updateConfig({ position });
            
            // 更新按钮状态
            document.querySelectorAll('.position-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 显示测试通知
            globalNotificationManager.show(`通知位置已设置为: ${position}`, 'info');
        };
        
        // 更新配置
        window.updateConfig = function() {
            const maxNotifications = parseInt(document.getElementById('maxNotifications').value) || 5;
            
            globalNotificationManager.updateConfig({
                maxNotifications
            });
            
            globalNotificationManager.show(`配置已更新: 最大通知数 = ${maxNotifications}`, 'success');
        };
        
        // 清空所有通知
        window.clearAllNotifications = function() {
            globalNotificationManager.clear();
        };
        
        // 刷新统计
        window.refreshStats = function() {
            const managerStats = globalNotificationManager.getStatistics();
            const toolbarStats = globalToolbarNotifications.getStatistics();
            
            const statsDisplay = document.getElementById('statsDisplay');
            statsDisplay.innerHTML = `
通知管理器统计:
- 活跃通知: ${managerStats.active}
- 队列通知: ${managerStats.queued}
- 总通知数: ${managerStats.total}
- 最大通知数: ${managerStats.maxNotifications}

工具栏通知统计:
- 历史记录: ${toolbarStats.total}
- 按操作分组: ${JSON.stringify(toolbarStats.byAction, null, 2)}
- 按类型分组: ${JSON.stringify(toolbarStats.byType, null, 2)}
            `;
        };
        
        // 刷新历史
        window.refreshHistory = function() {
            const history = globalToolbarNotifications.getHistory(10);
            const historyList = document.getElementById('historyList');
            
            if (history.length === 0) {
                historyList.innerHTML = '<div class="history-item">暂无历史记录</div>';
                return;
            }
            
            historyList.innerHTML = history.map(item => `
                <div class="history-item">
                    <div>${item.action} - ${item.type}: ${item.message}</div>
                    <div class="history-time">${new Date(item.timestamp).toLocaleTimeString()}</div>
                </div>
            `).join('');
        };
        
        // 清空历史
        window.clearHistory = function() {
            globalToolbarNotifications.clearHistory();
            refreshHistory();
        };
        
        // 定期刷新统计
        setInterval(() => {
            refreshStats();
            refreshHistory();
        }, 5000);
        
        console.log('通知系统测试页面已加载');
    </script>
</body>
</html>
