<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据结构验证测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a9e;
        }
        .button.success {
            background: #28a745;
        }
        .button.error {
            background: #dc3545;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .statistics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007acc;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据结构验证测试 - </h1>
        <p>测试新的数据结构定义、验证函数和迁移工具的功能</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 数据结构创建测试</h3>
            <button class="button" onclick="testCreateMessage()">测试创建消息</button>
            <button class="button" onclick="testCreateAIReply()">测试创建AI回复</button>
            <button class="button" onclick="testCreateMessageVersion()">测试创建消息版本</button>
            <div id="createResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 数据验证测试</h3>
            <button class="button" onclick="testValidateValidMessage()">验证有效消息</button>
            <button class="button" onclick="testValidateInvalidMessage()">验证无效消息</button>
            <button class="button" onclick="testValidateAIReplyStates()">验证AI回复状态</button>
            <div id="validateResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. AI回复状态管理测试</h3>
            <button class="button" onclick="testAIReplyStatusUpdate()">测试状态更新</button>
            <button class="button" onclick="testAIReplyActiveCheck()">测试活跃状态检查</button>
            <button class="button" onclick="testAIReplyFinishedCheck()">测试完成状态检查</button>
            <div id="aiReplyResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 数据迁移测试</h3>
            <button class="button" onclick="testLegacyDataMigration()">测试旧数据迁移</button>
            <button class="button" onclick="testMigrationValidation()">测试迁移验证</button>
            <button class="button" onclick="testBackupRestore()">测试备份恢复</button>
            <div id="migrationResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. 边界情况测试</h3>
            <button class="button" onclick="testEdgeCases()">测试边界情况</button>
            <button class="button" onclick="testErrorHandling()">测试错误处理</button>
            <button class="button" onclick="testPerformance()">测试性能</button>
            <div id="edgeResult" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>测试统计</h2>
        <div class="statistics" id="statistics">
            <div class="stat-item">
                <div class="stat-value" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
    </div>

    <script type="module">
        import { 
            createMessage, 
            createMessageVersion, 
            createAIReply, 
            validateMessage,
            updateAIReplyStatus,
            isAIReplyActive,
            isAIReplyFinished,
            AIReplyStatus,
            AIReplyErrorType,
            MessageType
        } from '../scripts/types/message-types.js';
        
        import { 
            migrateMessagesToNewFormat, 
            restoreFromBackup, 
            getAvailableBackups,
            deleteBackup
        } from '../scripts/utils/data-migration.js';

        // 全局测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        function updateStatistics() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            const successRate = testStats.total > 0 ? Math.round((testStats.passed / testStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = `${successRate}%`;
        }

        function runTest(testName, testFunction) {
            testStats.total++;
            try {
                const result = testFunction();
                if (result === true || (result && result.success !== false)) {
                    testStats.passed++;
                    console.log(`✅ ${testName} - 通过`);
                    return { success: true, result };
                } else {
                    testStats.failed++;
                    console.log(`❌ ${testName} - 失败:`, result);
                    return { success: false, result };
                }
            } catch (error) {
                testStats.failed++;
                console.error(`❌ ${testName} - 错误:`, error);
                return { success: false, error: error.message };
            } finally {
                updateStatistics();
            }
        }

        // 导出测试函数到全局作用域
        window.testCreateMessage = testCreateMessage;
        window.testCreateAIReply = testCreateAIReply;
        window.testCreateMessageVersion = testCreateMessageVersion;
        window.testValidateValidMessage = testValidateValidMessage;
        window.testValidateInvalidMessage = testValidateInvalidMessage;
        window.testValidateAIReplyStates = testValidateAIReplyStates;
        window.testAIReplyStatusUpdate = testAIReplyStatusUpdate;
        window.testAIReplyActiveCheck = testAIReplyActiveCheck;
        window.testAIReplyFinishedCheck = testAIReplyFinishedCheck;
        window.testLegacyDataMigration = testLegacyDataMigration;
        window.testMigrationValidation = testMigrationValidation;
        window.testBackupRestore = testBackupRestore;
        window.testEdgeCases = testEdgeCases;
        window.testErrorHandling = testErrorHandling;
        window.testPerformance = testPerformance;

        // 1. 数据结构创建测试
        function testCreateMessage() {
            const results = [];

            // 测试基本消息创建
            const test1 = runTest('创建基本用户消息', () => {
                const message = createMessage('Hello World', true, MessageType.TEXT);
                return message && message.id && message.isUser && message.versions.length === 1;
            });
            results.push(`创建基本用户消息: ${test1.success ? '✅' : '❌'}`);

            // 测试消息结构完整性
            const test2 = runTest('验证消息结构完整性', () => {
                const message = createMessage('Test message');
                const requiredFields = ['id', 'isUser', 'timestamp', 'edited', 'collapsed', 'type', 'versions', 'currentVersionIndex', 'metadata'];
                return requiredFields.every(field => message.hasOwnProperty(field));
            });
            results.push(`验证消息结构完整性: ${test2.success ? '✅' : '❌'}`);

            showResult('createResult', results.join('\n'), test1.success && test2.success ? 'success' : 'error');
        }

        function testCreateAIReply() {
            const results = [];

            // 测试基本AI回复创建
            const test1 = runTest('创建基本AI回复', () => {
                const aiReply = createAIReply('AI response', AIReplyStatus.COMPLETED);
                return aiReply && aiReply.id && aiReply.content === 'AI response' && aiReply.status === AIReplyStatus.COMPLETED;
            });
            results.push(`创建基本AI回复: ${test1.success ? '✅' : '❌'}`);

            // 测试带选项的AI回复创建
            const test2 = runTest('创建带选项的AI回复', () => {
                const options = {
                    model: 'gpt-4',
                    tokens: 150,
                    duration: 2000
                };
                const aiReply = createAIReply('Advanced response', AIReplyStatus.COMPLETED, options);
                return aiReply.metadata.model === 'gpt-4' && aiReply.metadata.tokens === 150;
            });
            results.push(`创建带选项的AI回复: ${test2.success ? '✅' : '❌'}`);

            // 测试默认值
            const test3 = runTest('测试AI回复默认值', () => {
                const aiReply = createAIReply();
                return aiReply.content === '' && aiReply.status === AIReplyStatus.PENDING && aiReply.progress === 0;
            });
            results.push(`测试AI回复默认值: ${test3.success ? '✅' : '❌'}`);

            showResult('aiReplyResult', results.join('\n'), test1.success && test2.success && test3.success ? 'success' : 'error');
        }

        function testCreateMessageVersion() {
            const results = [];

            // 测试版本创建
            const test1 = runTest('创建消息版本', () => {
                const version = createMessageVersion('Version content');
                return version && version.id && version.content === 'Version content' && version.aiReply === null;
            });
            results.push(`创建消息版本: ${test1.success ? '✅' : '❌'}`);

            // 测试带AI回复的版本
            const test2 = runTest('创建带AI回复的版本', () => {
                const aiReply = createAIReply('AI response');
                const version = createMessageVersion('User message', aiReply);
                return version.aiReply && version.aiReply.content === 'AI response';
            });
            results.push(`创建带AI回复的版本: ${test2.success ? '✅' : '❌'}`);

            showResult('createResult', results.join('\n'), test1.success && test2.success ? 'success' : 'error');
        }

        // 2. 数据验证测试
        function testValidateValidMessage() {
            const results = [];

            const test1 = runTest('验证有效消息', () => {
                const message = createMessage('Valid message', true, MessageType.TEXT);
                const validation = validateMessage(message);
                return validation.isValid && validation.errors.length === 0;
            });
            results.push(`验证有效消息: ${test1.success ? '✅' : '❌'}`);

            const test2 = runTest('验证带AI回复的消息', () => {
                const message = createMessage('User message');
                const aiReply = createAIReply('AI response', AIReplyStatus.COMPLETED);
                message.versions[0].aiReply = aiReply;
                const validation = validateMessage(message);
                return validation.isValid && validation.statistics.aiRepliesCount === 1;
            });
            results.push(`验证带AI回复的消息: ${test2.success ? '✅' : '❌'}`);

            showResult('validateResult', results.join('\n'), test1.success && test2.success ? 'success' : 'error');
        }

        function testValidateInvalidMessage() {
            const results = [];

            const test1 = runTest('验证无效消息ID', () => {
                const message = createMessage('Test');
                message.id = null; // 无效ID
                const validation = validateMessage(message);
                return !validation.isValid && validation.errors.some(e => e.includes('ID无效'));
            });
            results.push(`验证无效消息ID: ${test1.success ? '✅' : '❌'}`);

            const test2 = runTest('验证无效版本索引', () => {
                const message = createMessage('Test');
                message.currentVersionIndex = 999; // 超出范围
                const validation = validateMessage(message);
                return !validation.isValid && validation.errors.some(e => e.includes('版本索引无效'));
            });
            results.push(`验证无效版本索引: ${test2.success ? '✅' : '❌'}`);

            showResult('validateResult', results.join('\n'), test1.success && test2.success ? 'success' : 'error');
        }

        function testValidateAIReplyStates() {
            const results = [];

            const test1 = runTest('验证AI回复状态一致性', () => {
                const message = createMessage('Test');
                const aiReply = createAIReply('Response', AIReplyStatus.COMPLETED);
                aiReply.progress = 50; // 不一致的进度
                message.versions[0].aiReply = aiReply;
                const validation = validateMessage(message);
                return validation.warnings.some(w => w.includes('状态为完成但进度不是100%'));
            });
            results.push(`验证AI回复状态一致性: ${test1.success ? '✅' : '❌'}`);

            showResult('validateResult', results.join('\n'), test1.success ? 'success' : 'error');
        }

        // 3. AI回复状态管理测试
        function testAIReplyStatusUpdate() {
            const results = [];

            const test1 = runTest('更新AI回复状态', () => {
                const aiReply = createAIReply('Test', AIReplyStatus.PENDING);
                const updated = updateAIReplyStatus(aiReply, AIReplyStatus.GENERATING, { progress: 50 });
                return updated.status === AIReplyStatus.GENERATING && updated.progress === 50;
            });
            results.push(`更新AI回复状态: ${test1.success ? '✅' : '❌'}`);

            const test2 = runTest('自动设置完成状态', () => {
                const aiReply = createAIReply('Test', AIReplyStatus.GENERATING);
                const updated = updateAIReplyStatus(aiReply, AIReplyStatus.COMPLETED);
                return updated.progress === 100 && updated.endTimestamp;
            });
            results.push(`自动设置完成状态: ${test2.success ? '✅' : '❌'}`);

            showResult('aiReplyResult', results.join('\n'), test1.success && test2.success ? 'success' : 'error');
        }

        function testAIReplyActiveCheck() {
            const results = [];

            const test1 = runTest('检查活跃状态', () => {
                const pendingReply = createAIReply('Test', AIReplyStatus.PENDING);
                const generatingReply = createAIReply('Test', AIReplyStatus.GENERATING);
                const completedReply = createAIReply('Test', AIReplyStatus.COMPLETED);

                return isAIReplyActive(pendingReply) &&
                       isAIReplyActive(generatingReply) &&
                       !isAIReplyActive(completedReply);
            });
            results.push(`检查活跃状态: ${test1.success ? '✅' : '❌'}`);

            showResult('aiReplyResult', results.join('\n'), test1.success ? 'success' : 'error');
        }

        function testAIReplyFinishedCheck() {
            const results = [];

            const test1 = runTest('检查完成状态', () => {
                const completedReply = createAIReply('Test', AIReplyStatus.COMPLETED);
                const errorReply = createAIReply('Test', AIReplyStatus.ERROR);
                const generatingReply = createAIReply('Test', AIReplyStatus.GENERATING);

                return isAIReplyFinished(completedReply) &&
                       isAIReplyFinished(errorReply) &&
                       !isAIReplyFinished(generatingReply);
            });
            results.push(`检查完成状态: ${test1.success ? '✅' : '❌'}`);

            showResult('aiReplyResult', results.join('\n'), test1.success ? 'success' : 'error');
        }

        // 4. 数据迁移测试
        function testLegacyDataMigration() {
            const results = [];

            const test1 = runTest('迁移旧格式数据', () => {
                const legacyData = [
                    {
                        id: 'user1',
                        content: 'Hello',
                        isUser: true,
                        timestamp: Date.now(),
                        versions: [{
                            content: 'Hello',
                            timestamp: Date.now(),
                            aiReply: 'Hi there!'
                        }]
                    },
                    {
                        id: 'ai1',
                        content: 'Hi there!',
                        isUser: false,
                        timestamp: Date.now()
                    }
                ];

                const migrationResult = migrateMessagesToNewFormat(legacyData);
                return migrationResult.messages.length === 1 &&
                       migrationResult.messages[0].isUser &&
                       migrationResult.messages[0].versions[0].aiReply;
            });
            results.push(`迁移旧格式数据: ${test1.success ? '✅' : '❌'}`);

            showResult('migrationResult', results.join('\n'), test1.success ? 'success' : 'error');
        }

        function testMigrationValidation() {
            const results = [];

            const test1 = runTest('验证迁移结果', () => {
                const legacyData = [
                    {
                        id: 'user1',
                        content: 'Test message',
                        isUser: true,
                        timestamp: Date.now()
                    }
                ];

                const migrationResult = migrateMessagesToNewFormat(legacyData);
                return migrationResult.statistics.userMessages === 1 &&
                       migrationResult.errors.length === 0;
            });
            results.push(`验证迁移结果: ${test1.success ? '✅' : '❌'}`);

            showResult('migrationResult', results.join('\n'), test1.success ? 'success' : 'error');
        }

        function testBackupRestore() {
            const results = [];

            const test1 = runTest('备份和恢复功能', () => {
                // 这个测试需要实际的localStorage操作
                const testData = [createMessage('Test backup')];

                // 模拟备份（实际测试中会使用真实的备份函数）
                localStorage.setItem('test-backup', JSON.stringify({
                    timestamp: Date.now(),
                    data: testData
                }));

                const restored = localStorage.getItem('test-backup');
                localStorage.removeItem('test-backup');

                return restored !== null;
            });
            results.push(`备份和恢复功能: ${test1.success ? '✅' : '❌'}`);

            showResult('migrationResult', results.join('\n'), test1.success ? 'success' : 'error');
        }

        // 5. 边界情况测试
        function testEdgeCases() {
            const results = [];

            const test1 = runTest('空内容消息', () => {
                const message = createMessage('');
                const validation = validateMessage(message);
                return validation.isValid; // 空内容应该是有效的
            });
            results.push(`空内容消息: ${test1.success ? '✅' : '❌'}`);

            const test2 = runTest('大量版本的消息', () => {
                const message = createMessage('Initial');
                // 添加多个版本
                for (let i = 0; i < 100; i++) {
                    message.versions.push(createMessageVersion(`Version ${i}`));
                }
                message.currentVersionIndex = 50;
                const validation = validateMessage(message);
                return validation.isValid && validation.statistics.versionsCount === 101;
            });
            results.push(`大量版本的消息: ${test2.success ? '✅' : '❌'}`);

            showResult('edgeResult', results.join('\n'), test1.success && test2.success ? 'success' : 'error');
        }

        function testErrorHandling() {
            const results = [];

            const test1 = runTest('处理null输入', () => {
                try {
                    const validation = validateMessage(null);
                    return !validation.isValid;
                } catch (error) {
                    return true; // 应该抛出错误或返回无效结果
                }
            });
            results.push(`处理null输入: ${test1.success ? '✅' : '❌'}`);

            showResult('edgeResult', results.join('\n'), test1.success ? 'success' : 'error');
        }

        function testPerformance() {
            const results = [];

            const test1 = runTest('性能测试', () => {
                const startTime = performance.now();

                // 创建大量消息进行性能测试
                for (let i = 0; i < 1000; i++) {
                    const message = createMessage(`Message ${i}`);
                    validateMessage(message);
                }

                const endTime = performance.now();
                const duration = endTime - startTime;

                results.push(`创建和验证1000条消息耗时: ${duration.toFixed(2)}ms`);
                return duration < 1000; // 应该在1秒内完成
            });
            results.push(`性能测试: ${test1.success ? '✅' : '❌'}`);

            showResult('edgeResult', results.join('\n'), test1.success ? 'success' : 'error');
        }
    </script>
</body>
</html>
